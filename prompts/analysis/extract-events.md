# Extract Events from Obligation

You are a legal contract analysis expert. Your task is to identify and extract all events that trigger, modify, or terminate an obligation.

## Input Information
- **Obligation Text**: {{OBLIGATION_TEXT}}

## Instructions

1. **Identify Events**: Find all specific events, occurrences, or actions that affect the obligation.

2. **Event Types**: Look for these types of events:
   - **Triggering events**: Events that activate or start the obligation
   - **Modifying events**: Events that change how the obligation must be performed
   - **Terminating events**: Events that end or cancel the obligation
   - **Milestone events**: Significant points in the obligation's lifecycle
   - **Default events**: Events that constitute a breach or failure
   - **Force majeure events**: Unforeseeable circumstances that excuse performance

3. **Event Indicators**: Look for these event-related phrases:
   - "upon", "when", "after", "before", "during"
   - "in case of", "in the event of", "following"
   - "breach", "default", "failure", "violation"
   - "completion", "delivery", "acceptance", "approval"
   - "termination", "expiration", "cancellation"
   - "force majeure", "act of God", "unforeseeable circumstances"

4. **Event Characteristics**: For each event, identify:
   - **Actor**: Who or what causes the event
   - **Action**: What specifically happens
   - **Timing**: When the event occurs relative to the obligation
   - **Effect**: How the event impacts the obligation

## Output Format

Return a JSON array of events. Each event should have this structure:

```json
[
  {
    "text": "The complete text describing the event as it appears",
    "type": "triggering|modifying|terminating|milestone|default|force_majeure",
    "actor": "Who or what causes this event (party name, external factor, etc.)",
    "action": "The specific action or occurrence that constitutes the event",
    "timing": "When this event occurs (before, during, after the obligation)",
    "effect": "How this event affects the obligation (starts, changes, ends, etc.)"
  }
]
```

## Important Guidelines

- **Be Specific**: Extract events exactly as described in the text
- **Be Complete**: Don't miss any events that affect the obligation
- **Identify Actors**: Clearly specify who or what causes each event
- **Understand Timing**: Explain when events occur relative to the obligation
- **Describe Effects**: Clearly explain how each event impacts the obligation

## Examples

**Input Obligation**: "The Contractor shall provide maintenance services until the Client terminates the agreement or upon material breach by either party."

**Output**:
```json
[
  {
    "text": "the Client terminates the agreement",
    "type": "terminating",
    "actor": "Client",
    "action": "terminates the agreement",
    "timing": "during",
    "effect": "Ends the maintenance obligation"
  },
  {
    "text": "upon material breach by either party",
    "type": "default",
    "actor": "either party",
    "action": "material breach",
    "timing": "during",
    "effect": "Terminates the maintenance obligation"
  }
]
```

**Input Obligation**: "Payment shall be made within 30 days after delivery and acceptance of the goods."

**Output**:
```json
[
  {
    "text": "delivery and acceptance of the goods",
    "type": "triggering",
    "actor": "Contractor and Client",
    "action": "delivery and acceptance of the goods",
    "timing": "before",
    "effect": "Starts the 30-day payment period"
  }
]
```

**Input Obligation**: "The warranty shall be extended by 6 months if defects are found during the initial warranty period."

**Output**:
```json
[
  {
    "text": "if defects are found during the initial warranty period",
    "type": "modifying",
    "actor": "Client or third party",
    "action": "defects are found",
    "timing": "during",
    "effect": "Extends the warranty obligation by 6 months"
  }
]
```

## Edge Cases to Handle

- **Compound events**: Break down complex events into simpler components
- **Implied events**: Only extract explicitly mentioned events
- **Conditional events**: Include the full conditional context
- **Recurring events**: Identify events that may happen multiple times
- **External events**: Include events caused by third parties or circumstances

## Quality Checks

Before returning your response:
1. Verify each event text exactly matches the obligation
2. Confirm event types are correctly classified
3. Ensure actors are clearly identified
4. Check that timing relationships are accurate
5. Validate that effects on the obligation are clearly described

Return only the JSON array with no additional text or formatting.
