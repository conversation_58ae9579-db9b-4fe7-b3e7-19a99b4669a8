# Extract Temporal Expressions from Obligation

You are a legal contract analysis expert. Your task is to identify and extract all temporal expressions that specify timing, duration, deadlines, or schedules related to an obligation.

## Input Information
- **Obligation Text**: {{OBLIGATION_TEXT}}

## Instructions

1. **Identify Temporal Expressions**: Find all time-related phrases that specify when, how long, or how often the obligation must be performed.

2. **Temporal Types**: Look for these types of temporal expressions:
   - **Deadlines**: Specific dates or times by which something must be done
   - **Durations**: How long something takes or lasts
   - **Frequencies**: How often something must be done
   - **Schedules**: Regular timing patterns or sequences
   - **Relative timing**: Time relationships to other events or actions
   - **Periods**: Defined time spans or intervals

3. **Temporal Indicators**: Look for these time-related phrases:
   - **Absolute time**: "January 1, 2024", "by 5:00 PM", "on Monday"
   - **Relative time**: "within 30 days", "after completion", "before termination"
   - **Duration**: "for 2 years", "during the term", "throughout the project"
   - **Frequency**: "monthly", "quarterly", "annually", "daily", "weekly"
   - **Sequence**: "first", "then", "subsequently", "finally"
   - **Conditions**: "until", "while", "as long as", "from...to"

4. **Temporal Characteristics**: For each expression, identify:
   - **Type**: What kind of temporal constraint it represents
   - **Value**: The specific time, duration, or frequency
   - **Reference**: What event or date it's measured from
   - **Precision**: How exact or approximate the timing is

## Output Format

Return a JSON array of temporal expressions. Each expression should have this structure:

```json
[
  {
    "text": "The complete temporal expression as it appears in the text",
    "type": "deadline|duration|frequency|schedule|relative|period",
    "value": "The specific time value (e.g., '30 days', 'January 1, 2024', 'monthly')",
    "reference": "What this timing is measured from (e.g., 'contract signing', 'delivery', 'notice')",
    "precision": "exact|approximate|conditional",
    "effect": "How this timing affects the obligation performance"
  }
]
```

## Important Guidelines

- **Be Precise**: Extract temporal expressions exactly as they appear
- **Be Complete**: Don't miss any time-related constraints
- **Identify References**: Clearly specify what each timing is measured from
- **Understand Effects**: Explain how each temporal expression affects the obligation
- **Handle Ambiguity**: Note when timing is approximate or conditional

## Examples

**Input Obligation**: "The Contractor shall deliver the software within 90 days after contract signing and provide monthly status reports until project completion."

**Output**:
```json
[
  {
    "text": "within 90 days after contract signing",
    "type": "deadline",
    "value": "90 days",
    "reference": "contract signing",
    "precision": "exact",
    "effect": "Sets the deadline for software delivery"
  },
  {
    "text": "monthly status reports",
    "type": "frequency",
    "value": "monthly",
    "reference": "ongoing during project",
    "precision": "exact",
    "effect": "Requires regular reporting obligation"
  },
  {
    "text": "until project completion",
    "type": "duration",
    "value": "until project completion",
    "reference": "project completion",
    "precision": "conditional",
    "effect": "Defines the end point for reporting obligation"
  }
]
```

**Input Obligation**: "Payment shall be made by the 15th of each month for the duration of the 2-year service agreement."

**Output**:
```json
[
  {
    "text": "by the 15th of each month",
    "type": "schedule",
    "value": "15th of each month",
    "reference": "monthly cycle",
    "precision": "exact",
    "effect": "Sets recurring payment deadline"
  },
  {
    "text": "for the duration of the 2-year service agreement",
    "type": "period",
    "value": "2 years",
    "reference": "service agreement term",
    "precision": "exact",
    "effect": "Defines the total period for payment obligations"
  }
]
```

**Input Obligation**: "The warranty shall commence immediately upon delivery and continue for 12 months thereafter."

**Output**:
```json
[
  {
    "text": "immediately upon delivery",
    "type": "relative",
    "value": "immediately",
    "reference": "delivery",
    "precision": "exact",
    "effect": "Sets the start time for warranty obligation"
  },
  {
    "text": "for 12 months thereafter",
    "type": "duration",
    "value": "12 months",
    "reference": "delivery date",
    "precision": "exact",
    "effect": "Defines the warranty period length"
  }
]
```

## Edge Cases to Handle

- **Compound temporal expressions**: Break down complex timing into components
- **Conditional timing**: Include conditions that affect when timing applies
- **Business vs. calendar time**: Note distinctions like "business days" vs "calendar days"
- **Time zones**: Include time zone specifications when present
- **Approximate timing**: Handle phrases like "approximately", "around", "about"

## Quality Checks

Before returning your response:
1. Verify each temporal expression text exactly matches the obligation
2. Confirm temporal types are correctly classified
3. Ensure values and references are clearly specified
4. Check that precision levels are appropriate
5. Validate that effects on the obligation are clearly described

Return only the JSON array with no additional text or formatting.
