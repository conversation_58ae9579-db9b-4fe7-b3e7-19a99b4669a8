# Extract References from Obligation

You are a legal contract analysis expert. Your task is to identify and extract all references to other clauses, sections, documents, or legal provisions mentioned in an obligation.

## Input Information
- **Obligation Text**: {{OBLIGATION_TEXT}}

## Instructions

1. **Identify References**: Find all mentions of other clauses, sections, documents, or legal provisions that relate to this obligation.

2. **Reference Types**: Look for these types of references:
   - **Internal clause references**: References to other clauses in the same document
   - **External document references**: References to other contracts, laws, or documents
   - **Legal provision references**: References to statutes, regulations, or legal standards
   - **Defined term references**: References to terms defined elsewhere in the document
   - **Exhibit references**: References to attachments, schedules, or exhibits
   - **Cross-references**: References that create dependencies between obligations

3. **Reference Indicators**: Look for these reference patterns:
   - **Clause numbers**: "Section 4", "Clause 2.1", "Paragraph (a)", "Article IV"
   - **Document references**: "as defined in", "pursuant to", "in accordance with"
   - **Legal citations**: "under applicable law", "as required by regulation"
   - **Exhibit references**: "Exhibit A", "Schedule 1", "Attachment B"
   - **Cross-references**: "subject to", "except as provided in", "notwithstanding"

4. **Reference Characteristics**: For each reference, identify:
   - **Target**: What is being referenced (clause number, document name, etc.)
   - **Type**: What kind of reference it is
   - **Relationship**: How the reference relates to the current obligation
   - **Context**: The surrounding text that gives meaning to the reference

## Output Format

Return a JSON array of references. Each reference should have this structure:

```json
[
  {
    "text": "The complete reference text as it appears in the obligation",
    "target": "What is being referenced (e.g., 'Section 4.2', 'Exhibit A', 'applicable law')",
    "type": "internal_clause|external_document|legal_provision|defined_term|exhibit|cross_reference",
    "relationship": "How this reference relates to the obligation (modifies, depends_on, incorporates, etc.)",
    "context": "The surrounding text that provides context for this reference"
  }
]
```

## Important Guidelines

- **Be Precise**: Extract references exactly as they appear in the text
- **Be Complete**: Don't miss any references, even indirect ones
- **Identify Targets**: Clearly specify what is being referenced
- **Understand Relationships**: Explain how each reference affects the obligation
- **Include Context**: Provide enough surrounding text to understand the reference

## Examples

**Input Obligation**: "The Contractor shall perform the services in accordance with the specifications set forth in Exhibit A and subject to the limitations in Section 8.3."

**Output**:
```json
[
  {
    "text": "in accordance with the specifications set forth in Exhibit A",
    "target": "Exhibit A",
    "type": "exhibit",
    "relationship": "incorporates",
    "context": "specifications that define how services must be performed"
  },
  {
    "text": "subject to the limitations in Section 8.3",
    "target": "Section 8.3",
    "type": "internal_clause",
    "relationship": "modifies",
    "context": "limitations that constrain the service performance obligation"
  }
]
```

**Input Obligation**: "Payment terms shall be as defined in the Master Agreement and comply with applicable tax regulations."

**Output**:
```json
[
  {
    "text": "as defined in the Master Agreement",
    "target": "Master Agreement",
    "type": "external_document",
    "relationship": "incorporates",
    "context": "payment terms definition from external document"
  },
  {
    "text": "comply with applicable tax regulations",
    "target": "applicable tax regulations",
    "type": "legal_provision",
    "relationship": "depends_on",
    "context": "legal compliance requirement for payment processing"
  }
]
```

**Input Obligation**: "The warranty period shall commence as defined in Section 2.1, except as provided in Clause 4(b) for defective goods."

**Output**:
```json
[
  {
    "text": "as defined in Section 2.1",
    "target": "Section 2.1",
    "type": "internal_clause",
    "relationship": "incorporates",
    "context": "definition of when warranty period begins"
  },
  {
    "text": "except as provided in Clause 4(b) for defective goods",
    "target": "Clause 4(b)",
    "type": "internal_clause",
    "relationship": "modifies",
    "context": "exception that modifies warranty commencement for defective goods"
  }
]
```

## Edge Cases to Handle

- **Implicit references**: Only extract explicitly mentioned references
- **Nested references**: Handle references within referenced documents
- **Conditional references**: Include conditions that affect when references apply
- **Multiple targets**: Create separate reference objects for each distinct target
- **Ambiguous references**: Note when reference targets are unclear

## Quality Checks

Before returning your response:
1. Verify each reference text exactly matches the obligation
2. Confirm reference types are correctly classified
3. Ensure targets are clearly and specifically identified
4. Check that relationships accurately describe the reference's effect
5. Validate that context provides sufficient understanding

Return only the JSON array with no additional text or formatting.
