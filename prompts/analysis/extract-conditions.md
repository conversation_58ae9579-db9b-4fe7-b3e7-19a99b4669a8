# Extract Conditions from Obligation

You are a legal contract analysis expert. Your task is to identify and extract all conditions that affect when, how, or under what circumstances an obligation must be performed.

## Input Information
- **Obligation Text**: {{OBLIGATION_TEXT}}

## Instructions

1. **Identify Conditions**: Find all conditional statements that modify or qualify the obligation.

2. **Condition Types**: Look for these types of conditions:
   - **Precedent conditions**: Must be satisfied before the obligation becomes active
   - **Concurrent conditions**: Must be satisfied at the same time as the obligation
   - **Subsequent conditions**: May terminate or modify the obligation after it begins
   - **Performance conditions**: Specify how the obligation must be performed
   - **Time conditions**: Specify when the obligation must be performed
   - **Circumstantial conditions**: Specify under what circumstances the obligation applies

3. **Condition Indicators**: Look for these conditional phrases:
   - "if", "when", "unless", "provided that", "subject to"
   - "in the event that", "on condition that", "contingent upon"
   - "where", "while", "during", "until", "after", "before"
   - "except", "excluding", "save for", "other than"

4. **Logical Relationships**: Identify how conditions relate:
   - **AND**: All conditions must be met
   - **OR**: Any one condition must be met  
   - **NOT**: Condition must not be present

## Output Format

Return a JSON array of conditions. Each condition should have this structure:

```json
[
  {
    "text": "The complete text of the condition as it appears",
    "type": "precedent|concurrent|subsequent|performance|time|circumstantial",
    "logic": "AND|OR|NOT",
    "trigger": "The specific event or circumstance that triggers this condition",
    "effect": "How this condition affects the obligation (activates, modifies, terminates, etc.)"
  }
]
```

## Important Guidelines

- **Be Precise**: Extract conditions exactly as they appear in the text
- **Be Complete**: Don't miss any conditional elements
- **Identify Logic**: Clearly specify how conditions combine (AND/OR/NOT)
- **Understand Effect**: Explain how each condition affects the obligation
- **Handle Complexity**: Break down complex nested conditions into simpler parts

## Examples

**Input Obligation**: "The Contractor shall deliver the software if the Client provides the required specifications and unless force majeure events occur."

**Output**:
```json
[
  {
    "text": "if the Client provides the required specifications",
    "type": "precedent",
    "logic": "AND",
    "trigger": "Client provides the required specifications",
    "effect": "Activates the delivery obligation"
  },
  {
    "text": "unless force majeure events occur",
    "type": "subsequent", 
    "logic": "NOT",
    "trigger": "force majeure events occur",
    "effect": "Suspends or terminates the delivery obligation"
  }
]
```

**Input Obligation**: "Payment shall be made within 30 days after invoice receipt, provided that all deliverables have been accepted."

**Output**:
```json
[
  {
    "text": "within 30 days after invoice receipt",
    "type": "time",
    "logic": "AND",
    "trigger": "invoice receipt",
    "effect": "Sets the deadline for payment obligation"
  },
  {
    "text": "provided that all deliverables have been accepted",
    "type": "precedent",
    "logic": "AND", 
    "trigger": "all deliverables have been accepted",
    "effect": "Must be satisfied before payment obligation becomes due"
  }
]
```

## Edge Cases to Handle

- **Implicit conditions**: Only extract explicitly stated conditions
- **Nested conditions**: Break down "if A and (B or C)" into separate condition objects
- **Negative conditions**: Properly handle "unless", "except", "other than"
- **Time-based conditions**: Distinguish between deadlines and triggering events
- **Multiple triggers**: Create separate conditions for each distinct trigger

## Quality Checks

Before returning your response:
1. Verify each condition text exactly matches the obligation
2. Confirm condition types are correctly classified
3. Ensure logical relationships (AND/OR/NOT) are accurate
4. Check that triggers and effects are clearly described
5. Validate that no conditions are missed or duplicated

Return only the JSON array with no additional text or formatting.
