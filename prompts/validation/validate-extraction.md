# Validate Obligation Extraction Results

You are a legal contract analysis expert. Your task is to review and validate the results of obligation extraction to ensure accuracy, completeness, and quality.

## Input Information
- **Original Clause Text**: {{CLAUSE_TEXT}}
- **Extracted Obligations**: {{EXTRACTED_OBLIGATIONS}}

## Instructions

1. **Review Completeness**: Check if all obligations in the clause have been identified and extracted.

2. **Verify Accuracy**: Ensure that each extracted obligation accurately represents what's stated in the clause.

3. **Validate Text Anchors**: Confirm that starting and ending quotes correctly identify the obligation text in the clause.

4. **Check Party Identification**: Verify that the responsible parties are correctly identified for each obligation.

5. **Assess Quality**: Evaluate the overall quality of the extraction and identify any issues.

## Validation Criteria

### Completeness Check
- Are all obligations present in the clause captured?
- Are any obligations missing or overlooked?
- Are any non-obligations incorrectly identified as obligations?

### Accuracy Check
- Does each obligation text exactly match what appears in the clause?
- Are party assignments correct and unambiguous?
- Are obligation types (performance, delivery, payment, etc.) appropriate?
- Are actions clearly and accurately described?

### Text Anchor Validation
- Do starting quotes exactly match the beginning of obligation text?
- Do ending quotes exactly match the end of obligation text?
- Are the quotes unique enough to identify the specific obligation?
- Can the full obligation text be extracted using these anchors?

### Quality Assessment
- Is the extraction consistent and systematic?
- Are similar obligations handled in the same way?
- Is the level of detail appropriate?
- Are there any ambiguities or unclear elements?

## Output Format

Return a JSON object with validation results:

```json
{
  "overallScore": 0.85,
  "isValid": true,
  "completeness": {
    "score": 0.9,
    "missedObligations": [],
    "falsePositives": [],
    "comments": "All major obligations captured"
  },
  "accuracy": {
    "score": 0.8,
    "issues": [
      {
        "obligationId": "OBL-123",
        "issue": "Party identification unclear",
        "severity": "medium",
        "suggestion": "Specify 'Contractor' instead of 'Provider'"
      }
    ],
    "comments": "Minor party identification issues"
  },
  "textAnchors": {
    "score": 0.85,
    "issues": [
      {
        "obligationId": "OBL-124",
        "issue": "Starting quote too short",
        "severity": "low",
        "suggestion": "Use longer starting quote for uniqueness"
      }
    ],
    "comments": "Most anchors are reliable"
  },
  "recommendations": [
    "Review party identification in obligation OBL-123",
    "Consider longer text anchors for better precision",
    "Overall extraction quality is good"
  ]
}
```

## Scoring Guidelines

### Overall Score (0.0 - 1.0)
- **0.9-1.0**: Excellent - Ready for production use
- **0.8-0.89**: Good - Minor issues that should be addressed
- **0.7-0.79**: Fair - Significant issues requiring review
- **0.6-0.69**: Poor - Major problems, needs rework
- **Below 0.6**: Unacceptable - Complete re-extraction needed

### Issue Severity Levels
- **high**: Critical issues that affect legal interpretation
- **medium**: Important issues that should be addressed
- **low**: Minor issues that could be improved

## Common Issues to Check

### Completeness Issues
- Missed obligations due to non-standard language
- Conditional obligations not properly captured
- Nested obligations within complex sentences
- Obligations split across multiple sentences

### Accuracy Issues
- Incorrect party identification
- Wrong obligation type classification
- Incomplete or truncated obligation text
- Misinterpreted actions or requirements

### Text Anchor Issues
- Quotes that don't exactly match the text
- Quotes that are too short and not unique
- Quotes that span multiple obligations
- Missing or empty quotes

### Quality Issues
- Inconsistent handling of similar obligations
- Over-segmentation of single obligations
- Under-segmentation of multiple obligations
- Ambiguous or unclear descriptions

## Validation Examples

**Good Extraction**:
```json
{
  "text": "The Contractor shall deliver the completed software by December 31, 2024",
  "startingQuote": "The Contractor shall deliver",
  "endingQuote": "software by December 31, 2024",
  "party": "Contractor",
  "action": "deliver the completed software by December 31, 2024"
}
```
✅ Clear party, precise text anchors, complete action description

**Poor Extraction**:
```json
{
  "text": "shall deliver software",
  "startingQuote": "shall",
  "endingQuote": "software",
  "party": "Unknown",
  "action": "deliver"
}
```
❌ Incomplete text, poor anchors, missing party, vague action

## Quality Checks

Before returning your response:
1. Verify all scores are between 0.0 and 1.0
2. Ensure issue severity levels are appropriate
3. Check that recommendations are actionable
4. Confirm that examples support the assessment
5. Validate that the overall assessment is fair and accurate

Return only the JSON object with no additional text or formatting.
