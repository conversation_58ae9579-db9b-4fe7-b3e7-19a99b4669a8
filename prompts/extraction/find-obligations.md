# Extract Obligations from Legal Clause

You are a legal contract analysis expert. Your task is to identify and extract all obligations, duties, and requirements from the provided legal clause.

## Input Information
- **Clause Number**: {{CLAUSE_NUMBER}}
- **Clause Text**: {{CLAUSE_TEXT}}

## Instructions

1. **Identify Obligations**: Find all statements that create legal obligations, duties, or requirements for any party.

2. **Extract Key Information**: For each obligation, extract:
   - The exact text of the obligation
   - The party responsible (who must perform the obligation)
   - The specific action required
   - Starting quote (first 3-8 words of the obligation text)
   - Ending quote (last 3-8 words of the obligation text)

3. **Obligation Types**: Look for these types of obligations:
   - **Performance obligations**: Actions that must be performed
   - **Delivery obligations**: Items that must be delivered or provided
   - **Payment obligations**: Financial responsibilities
   - **Compliance obligations**: Requirements to follow rules, laws, or standards
   - **Notification obligations**: Requirements to inform or communicate
   - **Maintenance obligations**: Ongoing responsibilities to maintain or support
   - **Reporting obligations**: Requirements to provide reports or updates

4. **Key Indicators**: Look for these obligation indicators:
   - "shall", "must", "will", "agrees to", "undertakes to"
   - "is required to", "is obligated to", "has the duty to"
   - "is responsible for", "commits to", "covenants to"
   - "guarantees", "warrants", "ensures"

## Output Format

Return a JSON array of obligations. Each obligation should have this structure:

```json
[
  {
    "text": "The complete text of the obligation as it appears in the clause",
    "type": "performance|delivery|payment|compliance|notification|maintenance|reporting",
    "party": "The party responsible for this obligation (e.g., 'Contractor', 'Client', 'Buyer', 'Seller')",
    "action": "The specific action required (verb phrase describing what must be done)",
    "startingQuote": "First 3-8 words of the obligation text",
    "endingQuote": "Last 3-8 words of the obligation text"
  }
]
```

## Important Guidelines

- **Be Precise**: Extract the exact text as it appears in the clause
- **Be Complete**: Don't miss any obligations, even minor ones
- **Be Accurate**: Ensure starting and ending quotes exactly match the text
- **Identify Parties**: Clearly identify who is responsible for each obligation
- **Use Context**: Consider the broader context to understand party roles
- **Handle Ambiguity**: If party identification is unclear, use the most likely interpretation based on context

## Examples

**Input Clause**: "The Contractor shall deliver the completed software by December 31st and must provide ongoing technical support for 12 months."

**Output**:
```json
[
  {
    "text": "The Contractor shall deliver the completed software by December 31st",
    "type": "delivery",
    "party": "Contractor",
    "action": "deliver the completed software by December 31st",
    "startingQuote": "The Contractor shall deliver",
    "endingQuote": "software by December 31st"
  },
  {
    "text": "must provide ongoing technical support for 12 months",
    "type": "maintenance",
    "party": "Contractor", 
    "action": "provide ongoing technical support for 12 months",
    "startingQuote": "must provide ongoing technical",
    "endingQuote": "support for 12 months"
  }
]
```

## Edge Cases to Handle

- **Conditional obligations**: Include the full conditional statement
- **Multiple parties**: Create separate obligations for each party
- **Nested obligations**: Break down complex obligations into simpler components
- **Cross-references**: Include the full obligation text even if it references other clauses
- **Implied obligations**: Only extract explicitly stated obligations

## Quality Checks

Before returning your response:
1. Verify each starting/ending quote exactly matches the obligation text
2. Confirm all parties are correctly identified
3. Ensure no obligations are missed or duplicated
4. Check that obligation types are appropriate
5. Validate that actions are clearly described

Return only the JSON array with no additional text or formatting.
