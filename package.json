{"name": "clause-obligation-extractor", "version": "1.0.0", "type": "module", "description": "Clause-level obligation and duty extraction system using Gemini LLM", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node --loader ts-node/esm src/index.ts", "dev": "node --loader ts-node/esm --watch src/index.ts", "test": "npm run build && node examples/test-system.js", "example:basic": "npm run build && node examples/basic-usage.js", "example:batch": "npm run build && node examples/batch-processing.js", "example:test": "npm run build && node examples/test-system.js"}, "dependencies": {"@google/genai": "^1.8.0", "@google/generative-ai": "^0.21.0", "bottleneck": "^2.19.5", "dotenv": "^16.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "ts-node": "^10.0.0", "typescript": "^5.0.0"}}