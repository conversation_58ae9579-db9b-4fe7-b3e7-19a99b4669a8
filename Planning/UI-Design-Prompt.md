# UI Design Prompt: Contract Obligation Visualization Interface

## Overview
Design a two-pane interface for visualizing contract obligations with advanced grouping and interactive data display capabilities.

## Data Structure
The interface should handle the following TypeScript data structure:

```typescript
{
  _id: ObjectId,
  contractId: ObjectId,
  clauseId: ObjectId,
  obligation: {
    statement: string,
    modality: 'DUTY' | 'RIGHT' | 'PROHIBITION',
    conditions: Array<{statement: string}>,
    events: Array<{name: string}>,
    temporalExpressions: Array<{expression: string, parsedDate?: Date}>,
    references: Array<{pointer: string, targetClauseId?: ObjectId}>
  },
  parties: string[], // Extracted parties involved
  tags: string[], // Auto-generated tags for categorization
  priority: number // Calculated priority score
}
```

## Layout Requirements

### Two-Pane Layout
- **Left Pane**: Contract clause text display
- **Right Pane**: Obligations visualization with nested data components

### Left Pane (Contract Clause)
- Display the original contract clause text
- Highlight relevant sections that correspond to selected obligations
- Provide clause navigation and search functionality

### Right Pane (Obligations)
- Main obligation statement prominently displayed
- Modality badge (DUTY/RIGHT/PROHIBITION) with distinct color coding
- Interactive nested data visualization:
  - **Conditions Array**: Timeline-style graphic, expandable/collapsible
  - **Events Array**: Event bubble display with hover details
  - **Temporal Expressions**: Calendar/timeline view with parsed dates
  - **References Array**: Clickable reference links with pointer text

## Interactive Features

### Nested Data Visualization
- **Conditions**: Timeline component showing conditional statements in sequence
- **Events**: Interactive event nodes with expandable details
- **Temporal Expressions**: Date picker/calendar integration for parsed dates
- **References**: Linked references with navigation to target clauses

### Grouping Options
Create a flexible grouping system with the following options:

#### Primary Grouping Options:
1. **By Party** → **By Modality** (nested)
2. **By Modality** → **By Party** (nested)

#### Grouping Controls:
- Toggle between grouping hierarchies
- Expand/collapse group sections
- Filter by specific parties or modalities
- Sort within groups by priority score

### Visual Elements
- **Priority Indicator**: Visual priority score (1-10 scale) with color gradient
- **Tags Display**: Chip-style tags with filtering capability
- **Modality Color Coding**:
  - DUTY: Blue/Navy
  - RIGHT: Green/Emerald  
  - PROHIBITION: Red/Crimson

## User Interaction Requirements

### Selection & Navigation
- Click obligation to highlight corresponding clause text
- Cross-reference navigation between related obligations
- Breadcrumb navigation for grouped views

### Filtering & Search
- Multi-select filters for parties, modalities, and tags
- Priority range slider
- Full-text search across obligation statements
- Date range filtering for temporal expressions

### Export & Sharing
- Export filtered obligation sets
- Share specific obligation views via URL
- Print-friendly formatting options

## Technical Considerations

### Responsive Design
- Collapsible panes for mobile/tablet views
- Scrollable obligation lists with virtual scrolling for performance
- Touch-friendly interaction elements

### Accessibility
- Screen reader compatibility
- Keyboard navigation support
- High contrast mode support
- Focus management for nested components

### Performance
- Lazy loading for large obligation sets
- Efficient rendering for expandable/collapsible elements
- Optimized grouping calculations
- Smooth animations and transitions

## Design Style Guidelines

### Modern Professional Aesthetic
- Clean, minimalist interface
- Consistent spacing and typography
- Subtle shadows and rounded corners
- Professional color palette

### Information Hierarchy
- Clear visual hierarchy for grouped data
- Consistent iconography for different data types
- Intuitive expand/collapse indicators
- Logical flow for nested information

## Success Metrics
- Users can quickly identify obligations by party and type
- Nested data is easily accessible and understandable
- Cross-references are intuitive to navigate
- Interface remains performant with large datasets (100+ obligations)