# Clause-Level Obligation and Duty Extraction System

## System Description

### Overview

This system analyzes individual legal contract clauses (not entire contracts) to extract obligations and duties. It uses keyword search to identify potential obligation-containing clauses, then sends each complete clause/paragraph to Gemini LLM for detailed extraction and analysis.

### Key Changes from Full Contract Analysis:

- **Input**: Single clause/paragraph instead of full contract
- **Focus**: Clause-by-clause processing with keyword pre-filtering
- **Queue**: Simplified Node.js-based queue management (no Redis required)
- **References**: Smart reference matching with normalization
- **Prompts**: All LLM prompts stored as .md files with placeholders

### Core Components:

1. **Keyword Search Engine**: Pre-filters clauses likely to contain obligations
2. **Clause Extractor**: Extracts complete paragraphs for analysis
3. **LLM Analysis Module**: Sends whole clauses to Gemini for extraction
4. **Reference Normalizer**: Matches clause references (handles "4(d)" vs "4d")
5. **Queue Manager**: Simple Node.js queue with rate limiting (using Bottleneck)
6. **Prompt Manager**: Loads prompts from .md files with placeholder substitution
7. **Flag System**: Marks clauses with unresolved references

## Architecture Diagram

```mermaid
graph TB
    subgraph "Input Layer"
        A[Single Clause Input] --> B[Clause Preprocessor]
    end

    subgraph "Keyword Search Phase"
        B --> C[Keyword Search Engine]
        C --> D{Contains Obligation Keywords?}
        D -->|No| E[Skip Clause]
        D -->|Yes| F[Extract Full Paragraph]
    end

    subgraph "LLM Extraction Phase"
        F --> G[Queue Manager - Bottleneck]
        G --> H{Rate Limit OK?}
        H -->|Wait| I[Backoff Timer]
        I --> H
        H -->|OK| J[Send to Gemini LLM]
        J --> K[Extract Obligations & Duties]
    end

    subgraph "Analysis & Validation"
        K --> L[Extract Sub-elements]
        L --> M[Conditions]
        L --> N[Events]
        L --> O[Temporal Expressions]
        L --> P[References]
    end

    subgraph "Reference Resolution"
        P --> Q[Reference Normalizer]
        Q --> R[Match with Clause Numbering]
        R --> S{Reference Found?}
        S -->|No| T[Flag for Review]
        S -->|Yes| U[Link References]
    end

    subgraph "Output Layer"
        M --> V[Structured Obligation]
        N --> V
        O --> V
        U --> V
        T --> W[Human Review Queue]
        V --> X[Final Output]
    end

    style J fill:#4285f4
    style G fill:#ff9800
    style Q fill:#9c27b0
    style T fill:#f44336
```

## Detailed Implementation

### Phase 1: Keyword Search and Clause Extraction

```typescript
// services/keyword-search.service.ts
export class KeywordSearchService {
  private obligationKeywords = [
    "shall",
    "must",
    "will",
    "required to",
    "obligated to",
    "agrees to",
    "undertakes to",
    "commits to",
    "responsible for",
    "duty to",
    "covenant",
    "promise",
    "bound to",
    "liable for",
  ];

  /**
   * Search for clauses containing obligation keywords
   * Returns complete paragraphs/clauses for analysis
   */
  async findObligationClauses(clauseText: string): Promise<ClauseSearchResult> {
    const lowerText = clauseText.toLowerCase();

    // Check if clause contains any obligation keywords
    const foundKeywords = this.obligationKeywords.filter((keyword) =>
      lowerText.includes(keyword)
    );

    if (foundKeywords.length === 0) {
      return {
        containsObligations: false,
        keywords: [],
        fullClause: "",
      };
    }

    // Extract the complete paragraph/clause
    const fullClause = this.extractCompleteParagraph(clauseText);

    return {
      containsObligations: true,
      keywords: foundKeywords,
      fullClause: fullClause,
      confidence: this.calculateKeywordConfidence(foundKeywords),
    };
  }

  private extractCompleteParagraph(text: string): string {
    // Return the full text as it's already a single clause
    // In a full document context, this would extract the paragraph
    return text.trim();
  }

  private calculateKeywordConfidence(keywords: string[]): number {
    // Strong obligation indicators
    const strongIndicators = ["shall", "must", "required to", "obligated to"];
    const strongCount = keywords.filter((k) =>
      strongIndicators.includes(k)
    ).length;

    return Math.min(0.5 + strongCount * 0.2 + keywords.length * 0.1, 1.0);
  }
}
```

### Phase 2: Prompt Management System

```typescript
// services/prompt-manager.service.ts
import * as fs from "fs/promises";
import * as path from "path";

export class PromptManagerService {
  private promptsDir = path.join(process.cwd(), "prompts");
  private promptCache = new Map<string, string>();

  /**
   * Load prompt from .md file and replace placeholders
   */
  async loadPrompt(
    promptPath: string,
    variables: Record<string, any>
  ): Promise<string> {
    const fullPath = path.join(this.promptsDir, promptPath);

    // Check cache first
    let template = this.promptCache.get(fullPath);
    if (!template) {
      template = await fs.readFile(fullPath, "utf-8");
      this.promptCache.set(fullPath, template);
    }

    // Replace placeholders
    return this.replacePlaceholders(template, variables);
  }

  private replacePlaceholders(
    template: string,
    variables: Record<string, any>
  ): string {
    let result = template;

    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, "g"), String(value));
    }

    return result;
  }

  /**
   * Get system context for different prompt types
   */
  getSystemContext(type: "extraction" | "validation" | "analysis"): string {
    const contexts = {
      extraction:
        "You are a legal contract analyzer specializing in identifying obligations and duties in contract clauses.",
      validation:
        "You are an expert at validating legal obligation extractions for completeness and accuracy.",
      analysis:
        "You are an expert at analyzing the detailed components of legal obligations.",
    };

    return contexts[type];
  }
}
```

### Phase 3: Simplified Queue Management with Bottleneck

```typescript
// services/queue-manager.service.ts
import Bottleneck from "bottleneck";
import { GoogleGenerativeAI } from "@google/generative-ai";

export class QueueManagerService {
  private limiter: Bottleneck;
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    // Initialize Bottleneck with Gemini rate limits
    this.limiter = new Bottleneck({
      minTime: 1000, // Minimum time between requests (1 second)
      maxConcurrent: 5, // Maximum concurrent requests
      reservoir: 60, // Token bucket size (requests per minute)
      reservoirRefreshAmount: 60,
      reservoirRefreshInterval: 60 * 1000, // Refill every minute

      // Retry configuration
      retryDelay: 2000,
      maxRetryTime: 30000,
    });

    // Initialize Gemini
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
    this.model = this.genAI.getGenerativeModel({
      model: "gemini-2.5-flash",
      generationConfig: {
        temperature: 0.2,
        maxOutputTokens: 8192,
      },
    });

    // Set up error handling
    this.limiter.on("error", (error) => {
      console.error("Queue error:", error);
    });

    this.limiter.on("retry", (error, jobInfo) => {
      console.log(
        `Retrying job ${jobInfo.options.id} after error:`,
        error.message
      );
    });
  }

  /**
   * Queue a request to Gemini with automatic rate limiting
   */
  async queueRequest(
    prompt: string,
    context: string,
    priority?: number
  ): Promise<string> {
    return this.limiter.schedule(
      { priority: priority || 5 }, // Higher number = higher priority
      async () => this.callGemini(prompt, context)
    );
  }

  private async callGemini(prompt: string, context: string): Promise<string> {
    try {
      const result = await this.model.generateContent({
        contents: [
          {
            role: "user",
            parts: [{ text: prompt }],
          },
        ],
        systemInstruction: context,
      });

      return result.response.text();
    } catch (error: any) {
      if (error.status === 429) {
        // Rate limit - Bottleneck will retry
        throw new Error("RATE_LIMIT_EXCEEDED");
      }
      throw error;
    }
  }

  /**
   * Get current queue statistics
   */
  getQueueStats() {
    return {
      running: this.limiter.running(),
      queued: this.limiter.queued(),
      done: this.limiter.done(),
    };
  }
}
```

### Phase 4: Clause-Level Obligation Extraction

```typescript
// services/obligation-extractor.service.ts
export class ObligationExtractorService {
  constructor(
    private keywordSearch: KeywordSearchService,
    private queueManager: QueueManagerService,
    private promptManager: PromptManagerService
  ) {}

  /**
   * Extract obligations from a single clause
   * This is the main entry point for clause analysis
   */
  async extractObligationsFromClause(
    clauseText: string,
    clauseNumber?: string
  ): Promise<ClauseAnalysisResult> {
    console.log(`Analyzing clause ${clauseNumber || "unnamed"}...`);

    // Step 1: Keyword search to determine if clause needs analysis
    const searchResult = await this.keywordSearch.findObligationClauses(
      clauseText
    );

    if (!searchResult.containsObligations) {
      return {
        clauseNumber,
        clauseText,
        containsObligations: false,
        obligations: [],
        skippedReason: "No obligation keywords found",
      };
    }

    // Step 2: Send full clause to Gemini for extraction
    const obligations = await this.extractWithLLM(
      searchResult.fullClause,
      clauseNumber
    );

    // Step 3: Analyze sub-elements for each obligation
    const analyzedObligations = await this.analyzeObligationDetails(
      obligations
    );

    // Step 4: Resolve references and flag unresolved ones
    const finalObligations = await this.resolveReferences(
      analyzedObligations,
      clauseNumber
    );

    return {
      clauseNumber,
      clauseText: searchResult.fullClause,
      containsObligations: true,
      obligations: finalObligations,
      keywordsFound: searchResult.keywords,
      confidence: searchResult.confidence,
    };
  }

  private async extractWithLLM(
    clauseText: string,
    clauseNumber?: string
  ): Promise<Obligation[]> {
    // Load prompt from file
    const prompt = await this.promptManager.loadPrompt(
      "extraction/find-obligations.md",
      {
        CLAUSE_TEXT: clauseText,
        CLAUSE_NUMBER: clauseNumber || "N/A",
      }
    );

    const context = this.promptManager.getSystemContext("extraction");

    try {
      const response = await this.queueManager.queueRequest(prompt, context, 1);
      const obligations = JSON.parse(response);

      return obligations.map((obl: any, index: number) => ({
        id: `OBL-${Date.now()}-${index}`,
        clauseNumber,
        text: obl.text,
        type: obl.type,
        party: obl.party,
        action: obl.action,
        conditions: [],
        events: [],
        temporalExpressions: [],
        references: [],
        validationStatus: "pending",
        unresolvedReferences: [],
      }));
    } catch (error) {
      console.error("LLM extraction error:", error);
      throw error;
    }
  }

  private async analyzeObligationDetails(
    obligations: Obligation[]
  ): Promise<Obligation[]> {
    // Process obligations in parallel with rate limiting handled by queue
    const analysisPromises = obligations.map(async (obligation) => {
      const [conditions, events, temporal, references] = await Promise.all([
        this.extractConditions(obligation),
        this.extractEvents(obligation),
        this.extractTemporalExpressions(obligation),
        this.extractReferences(obligation),
      ]);

      return {
        ...obligation,
        conditions,
        events,
        temporalExpressions: temporal,
        references,
      };
    });

    return Promise.all(analysisPromises);
  }

  private async extractConditions(
    obligation: Obligation
  ): Promise<Condition[]> {
    const prompt = await this.promptManager.loadPrompt(
      "analysis/extract-conditions.md",
      { OBLIGATION_TEXT: obligation.text }
    );

    const response = await this.queueManager.queueRequest(
      prompt,
      this.promptManager.getSystemContext("analysis"),
      2
    );

    return JSON.parse(response);
  }

  // Similar methods for events, temporal expressions, and references...
}
```

### Phase 5: Reference Normalization and Resolution

```typescript
// services/reference-resolver.service.ts
export class ReferenceResolverService {
  /**
   * Normalize clause references to handle variations
   * Examples: "4(d)" -> "4d", "Clause 4.1" -> "4.1", "Section IV" -> "4"
   */
  normalizeReference(reference: string): string {
    let normalized = reference.toLowerCase();

    // Remove common prefixes
    normalized = normalized.replace(
      /^(clause|section|paragraph|article)\s*/i,
      ""
    );

    // Convert Roman numerals to Arabic
    normalized = this.convertRomanToArabic(normalized);

    // Remove special characters but keep alphanumeric
    normalized = normalized.replace(/[^a-z0-9]/g, "");

    return normalized;
  }

  /**
   * Create multiple possible variations of a reference
   */
  generateReferenceVariations(reference: string): string[] {
    const variations = new Set<string>();

    // Original
    variations.add(reference);

    // Normalized
    const normalized = this.normalizeReference(reference);
    variations.add(normalized);

    // With common prefixes
    const prefixes = ["clause", "section", ""];
    const baseRef = reference.replace(
      /^(clause|section|paragraph|article)\s*/i,
      ""
    );

    prefixes.forEach((prefix) => {
      if (prefix) {
        variations.add(`${prefix} ${baseRef}`);
        variations.add(`${prefix}${baseRef}`);
      }
    });

    // Handle parentheses variations: "4(d)" <-> "4d"
    if (reference.includes("(")) {
      variations.add(reference.replace(/[()]/g, ""));
    } else if (/\d[a-z]/.test(reference)) {
      // Add parentheses: "4d" -> "4(d)"
      variations.add(reference.replace(/(\d)([a-z])/i, "$1($2)"));
    }

    return Array.from(variations);
  }

  /**
   * Resolve references within an obligation
   */
  async resolveReferences(
    obligations: Obligation[],
    currentClauseNumber?: string
  ): Promise<Obligation[]> {
    const referenceIndex = this.buildReferenceIndex(obligations);

    return obligations.map((obligation) => {
      const unresolvedReferences: UnresolvedReference[] = [];
      const resolvedReferences: ResolvedReference[] = [];

      obligation.references.forEach((ref) => {
        const variations = this.generateReferenceVariations(ref.target);
        let resolved = false;

        // Try to find a match using any variation
        for (const variation of variations) {
          const normalizedVariation = this.normalizeReference(variation);
          if (referenceIndex.has(normalizedVariation)) {
            resolvedReferences.push({
              ...ref,
              resolvedTo: referenceIndex.get(normalizedVariation)!,
              confidence: this.calculateMatchConfidence(ref.target, variation),
            });
            resolved = true;
            break;
          }
        }

        if (!resolved) {
          unresolvedReferences.push({
            ...ref,
            reason: "No matching clause found",
            attemptedVariations: variations,
          });
        }
      });

      return {
        ...obligation,
        resolvedReferences,
        unresolvedReferences,
        needsReview: unresolvedReferences.length > 0,
      };
    });
  }

  private buildReferenceIndex(obligations: Obligation[]): Map<string, string> {
    const index = new Map<string, string>();

    obligations.forEach((obl) => {
      if (obl.clauseNumber) {
        // Add normalized version
        const normalized = this.normalizeReference(obl.clauseNumber);
        index.set(normalized, obl.clauseNumber);

        // Add common variations
        const variations = this.generateReferenceVariations(obl.clauseNumber);
        variations.forEach((v) => {
          index.set(this.normalizeReference(v), obl.clauseNumber);
        });
      }
    });

    return index;
  }

  private convertRomanToArabic(text: string): string {
    const romanNumerals: Record<string, number> = {
      i: 1,
      ii: 2,
      iii: 3,
      iv: 4,
      v: 5,
      vi: 6,
      vii: 7,
      viii: 8,
      ix: 9,
      x: 10,
    };

    return text.replace(/\b[ivx]+\b/g, (match) => {
      const value = romanNumerals[match];
      return value ? String(value) : match;
    });
  }

  private calculateMatchConfidence(original: string, matched: string): number {
    if (original === matched) return 1.0;
    if (this.normalizeReference(original) === this.normalizeReference(matched))
      return 0.9;
    return 0.7; // Fuzzy match
  }
}
```

### Phase 6: Text Anchor Utility Service

```typescript
// services/text-anchor.service.ts
export class TextAnchorService {
  /**
   * Extract the full supporting quote for an obligation using start/end anchors
   */
  extractSupportingQuote(
    clauseText: string,
    startingQuote: string,
    endingQuote: string
  ): ExtractedQuote | null {
    try {
      // Escape special regex characters in quotes
      const escapedStart = this.escapeRegex(startingQuote);
      const escapedEnd = this.escapeRegex(endingQuote);

      // Build regex pattern with non-greedy matching
      const pattern = new RegExp(
        `(${escapedStart}.*?${escapedEnd})`,
        "is" // case-insensitive, dotAll
      );

      const match = clauseText.match(pattern);

      if (match && match[1]) {
        const extractedText = match[1].trim();
        const startIndex = clauseText.indexOf(match[1]);

        return {
          text: extractedText,
          startIndex,
          endIndex: startIndex + extractedText.length,
          confidence: this.calculateExtractionConfidence(
            startingQuote,
            endingQuote,
            extractedText
          ),
        };
      }

      return null;
    } catch (error) {
      console.error("Regex extraction failed:", error);
      return null;
    }
  }

  /**
   * Generate optimal starting and ending quotes from obligation text
   * This can be used to validate/improve LLM-generated quotes
   */
  generateOptimalQuotes(
    obligationText: string,
    clauseText: string
  ): { startingQuote: string; endingQuote: string } {
    const words = obligationText.trim().split(/\s+/);

    // Try different lengths starting from 3 words
    for (let startLen = 3; startLen <= Math.min(8, words.length); startLen++) {
      for (let endLen = 3; endLen <= Math.min(8, words.length); endLen++) {
        const startQuote = words.slice(0, startLen).join(" ");
        const endQuote = words.slice(-endLen).join(" ");

        // Test if these quotes uniquely identify the obligation
        if (this.testQuoteUniqueness(clauseText, startQuote, endQuote)) {
          return { startingQuote: startQuote, endingQuote: endQuote };
        }
      }
    }

    // Fallback to longer quotes if needed
    return {
      startingQuote: words.slice(0, Math.min(8, words.length)).join(" "),
      endingQuote: words.slice(-Math.min(8, words.length)).join(" "),
    };
  }

  /**
   * Test if start/end quotes uniquely identify a single text span
   */
  private testQuoteUniqueness(
    clauseText: string,
    startQuote: string,
    endQuote: string
  ): boolean {
    const escapedStart = this.escapeRegex(startQuote);
    const escapedEnd = this.escapeRegex(endQuote);
    const pattern = new RegExp(`${escapedStart}.*?${escapedEnd}`, "gis");

    const matches = clauseText.match(pattern);
    return matches !== null && matches.length === 1;
  }

  /**
   * Escape special regex characters
   */
  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  }

  /**
   * Calculate confidence score for text extraction
   */
  private calculateExtractionConfidence(
    startQuote: string,
    endQuote: string,
    extractedText: string
  ): number {
    let confidence = 1.0;

    // Reduce confidence for very short quotes (less reliable)
    if (startQuote.split(" ").length < 3) confidence -= 0.2;
    if (endQuote.split(" ").length < 3) confidence -= 0.2;

    // Reduce confidence for very long extractions (might include extra text)
    if (extractedText.length > 500) confidence -= 0.1;

    // Check if extracted text actually starts/ends with the quotes
    if (!extractedText.toLowerCase().startsWith(startQuote.toLowerCase())) {
      confidence -= 0.3;
    }
    if (!extractedText.toLowerCase().endsWith(endQuote.toLowerCase())) {
      confidence -= 0.3;
    }

    return Math.max(0, confidence);
  }

  /**
   * Highlight obligation text within a clause for UI display
   */
  highlightObligationInClause(
    clauseText: string,
    obligations: Obligation[]
  ): HighlightedClause {
    const highlights: TextHighlight[] = [];

    obligations.forEach((obligation, index) => {
      const extracted = this.extractSupportingQuote(
        clauseText,
        obligation.startingQuote,
        obligation.endingQuote
      );

      if (extracted) {
        highlights.push({
          obligationId: obligation.id,
          startIndex: extracted.startIndex,
          endIndex: extracted.endIndex,
          text: extracted.text,
          cssClass: `obligation-highlight-${index}`,
          confidence: extracted.confidence,
        });
      }
    });

    // Sort highlights by start position
    highlights.sort((a, b) => a.startIndex - b.startIndex);

    return {
      originalText: clauseText,
      highlights,
      highlightedHtml: this.generateHighlightedHtml(clauseText, highlights),
    };
  }

  private generateHighlightedHtml(
    text: string,
    highlights: TextHighlight[]
  ): string {
    let html = "";
    let lastIndex = 0;

    highlights.forEach((highlight) => {
      // Add text before highlight
      html += this.escapeHtml(text.substring(lastIndex, highlight.startIndex));

      // Add highlighted text
      html += `<span class="${highlight.cssClass}" data-obligation-id="${highlight.obligationId}">`;
      html += this.escapeHtml(highlight.text);
      html += "</span>";

      lastIndex = highlight.endIndex;
    });

    // Add remaining text
    html += this.escapeHtml(text.substring(lastIndex));

    return html;
  }

  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#39;");
  }
}

// Additional types for text anchoring
export interface ExtractedQuote {
  text: string;
  startIndex: number;
  endIndex: number;
  confidence: number;
}

export interface TextHighlight {
  obligationId: string;
  startIndex: number;
  endIndex: number;
  text: string;
  cssClass: string;
  confidence: number;
}

export interface HighlightedClause {
  originalText: string;
  highlights: TextHighlight[];
  highlightedHtml: string;
}
```

### Phase 7: Human Review Flag System

```typescript
// services/review-flag.service.ts
export class ReviewFlagService {
  /**
   * Flag clauses that need human review
   */
  flagForReview(
    result: ClauseAnalysisResult,
    reason: ReviewReason
  ): ReviewItem {
    return {
      id: `REVIEW-${Date.now()}`,
      clauseNumber: result.clauseNumber,
      clauseText: result.clauseText,
      reason: reason.type,
      details: reason.details,
      priority: this.calculatePriority(reason),
      timestamp: new Date(),
      obligations: result.obligations.filter((o) => o.needsReview),
      suggestedActions: this.getSuggestedActions(reason),
    };
  }

  private calculatePriority(reason: ReviewReason): number {
    const priorityMap: Record<string, number> = {
      "unresolved-references": 8,
      "low-confidence": 5,
      "ambiguous-language": 6,
      "complex-conditions": 7,
      "extraction-failed": 10,
    };

    return priorityMap[reason.type] || 5;
  }

  private getSuggestedActions(reason: ReviewReason): string[] {
    switch (reason.type) {
      case "unresolved-references":
        return [
          "Verify if referenced clause exists",
          "Check for typos in reference",
          "Confirm reference numbering system",
        ];
      case "ambiguous-language":
        return [
          "Clarify party responsibilities",
          "Define ambiguous terms",
          "Specify concrete actions",
        ];
      default:
        return ["Manual review required"];
    }
  }
}
```

## Prompt File Structure

### Example: `/prompts/extraction/find-obligations.md`

````markdown
# Extract Obligations from Clause

You are analyzing a single legal contract clause to identify ALL obligations and duties.

## Clause Information

- Clause Number: {{CLAUSE_NUMBER}}
- Clause Text: {{CLAUSE_TEXT}}

## Task

Extract every obligation, duty, or requirement from this clause. Look for:

- Words like: shall, must, will, required to, obligated to, agrees to, undertakes to
- Any commitment or promise made by a party
- Any duty or responsibility assigned

## Output Format

Return ONLY a JSON array with this structure:

```json
[
  {
    "text": "exact obligation text from the clause",
    "type": "obligation|duty|requirement",
    "party": "party responsible",
    "action": "what must be done",
    "startingQuote": "first few words of obligation",
    "endingQuote": "last few words of obligation"
  }
]
```

**Important for startingQuote and endingQuote:**

- Use 1-8 words from the EXACT text as it appears in the clause
- startingQuote: First few words that uniquely identify where this obligation begins
- endingQuote: Last few words that uniquely identify where this obligation ends
- These will be used for regex matching, so they must match the clause text precisely
- Include punctuation if it's part of the identifying text
````

If no obligations are found, return an empty array: []

````

### Example: `/prompts/analysis/extract-conditions.md`

```markdown
# Extract Conditions from Obligation

Analyze the following obligation and extract ALL conditions, prerequisites, and exceptions.

## Obligation Text
{{OBLIGATION_TEXT}}

## What to Look For
- IF/WHEN clauses (prerequisites)
- UNLESS/EXCEPT clauses (exceptions)
- PROVIDED THAT clauses (qualifiers)
- Subject to/Conditional upon phrases
- Any limiting or qualifying language

## Output Format
Return a JSON array:
```json
[
  {
    "type": "prerequisite|exception|qualifier",
    "text": "condition text",
    "logic": "AND|OR|NOT"
  }
]
````

If no conditions exist, return empty array: []

````

## Data Types

```typescript
// types/clause-analysis.types.ts
export interface ClauseAnalysisResult {
  clauseNumber?: string;
  clauseText: string;
  containsObligations: boolean;
  obligations: Obligation[];
  keywordsFound?: string[];
  confidence?: number;
  skippedReason?: string;
}

export interface Obligation {
  id: string;
  clauseNumber?: string;
  text: string;
  type: 'duty' | 'obligation';
  party: string;
  action: string;

  // Text anchoring for regex extraction
  startingQuote: string; // First 1-8 words of obligation text
  endingQuote: string;   // Last 1-8 words of obligation text

  // Sub-elements
  conditions: Condition[];
  events: Event[];
  temporalExpressions: TemporalExpression[];
  references: Reference[];

  // Resolution tracking
  resolvedReferences?: ResolvedReference[];
  unresolvedReferences?: UnresolvedReference[];

  // Status
  validationStatus: 'pending' | 'validated' | 'flagged';
  needsReview: boolean;
}

export interface UnresolvedReference extends Reference {
  reason: string;
  attemptedVariations: string[];
}

export interface ResolvedReference extends Reference {
  resolvedTo: string;
  confidence: number;
}

export interface ReviewItem {
  id: string;
  clauseNumber?: string;
  clauseText: string;
  reason: string;
  details: any;
  priority: number;
  timestamp: Date;
  obligations: Obligation[];
  suggestedActions: string[];
}

export interface ReviewReason {
  type: 'unresolved-references' | 'low-confidence' | 'ambiguous-language' | 'complex-conditions' | 'extraction-failed';
  details: any;
}

export interface ClauseSearchResult {
  containsObligations: boolean;
  keywords: string[];
  fullClause: string;
  confidence?: number;
}
````

## Usage Example

```typescript
// Example: Analyzing a single clause
const extractor = new ObligationExtractorService(
  new KeywordSearchService(),
  new QueueManagerService(),
  new PromptManagerService()
);

const clauseText =
  "The Contractor shall complete all work by December 31, 2024, provided that all necessary permits have been obtained as referenced in Section 4(d).";

const result = await extractor.extractObligationsFromClause(clauseText, "7.2");

// Example result:
{
  "clauseNumber": "7.2",
  "clauseText": "The Contractor shall complete all work by December 31, 2024, provided that all necessary permits have been obtained as referenced in Section 4(d).",
  "containsObligations": true,
  "obligations": [
    {
      "id": "OBL-1703872800000-0",
      "clauseNumber": "7.2",
      "text": "The Contractor shall complete all work by December 31, 2024",
      "type": "obligation",
      "party": "Contractor",
      "action": "complete all work",
      "startingQuote": "The Contractor shall complete",
      "endingQuote": "by December 31, 2024",
      "conditions": [
        {
          "type": "prerequisite",
          "text": "provided that all necessary permits have been obtained",
          "logic": "AND"
        }
      ],
      "events": [],
      "temporalExpressions": [
        {
          "text": "by December 31, 2024",
          "type": "deadline"
        }
      ],
      "references": [
        {
          "target": "Section 4(d)",
          "type": "clause_reference"
        }
      ],
      "resolvedReferences": [
        {
          "target": "Section 4(d)",
          "resolvedTo": "4d",
          "confidence": 0.9
        }
      ],
      "unresolvedReferences": [],
      "validationStatus": "pending",
      "needsReview": false
    }
  ],
  "keywordsFound": ["shall"],
  "confidence": 0.8
}

// Using the text anchors to extract supporting quote:
const textAnchor = new TextAnchorService();
const supportingQuote = textAnchor.extractSupportingQuote(
  clauseText,
  "The Contractor shall complete",
  "by December 31, 2024"
);
// Returns: "The Contractor shall complete all work by December 31, 2024"

// Result includes:
// - Extracted obligations with party, action, deadline
// - startingQuote and endingQuote for precise text location
// - Conditions (permit requirement)
// - Temporal expressions (December 31, 2024)
// - References (Section 4(d)) with resolution status
// - Flags if Section 4(d) cannot be resolved
```

## Key Benefits of This Architecture

1. **Clause-Focused**: Analyzes individual clauses instead of entire contracts
2. **Efficient Pre-filtering**: Keyword search reduces unnecessary LLM calls
3. **Simple Queue Management**: Bottleneck handles rate limiting without external dependencies
4. **Smart Reference Matching**: Handles common variations in clause numbering
5. **Clear Review Flags**: Automatically identifies clauses needing human attention
6. **Maintainable Prompts**: All prompts in version-controlled .md files
7. **Flexible Architecture**: Easy to extend with new extraction types

## Implementation Priority

1. Implement keyword search and clause extraction
2. Set up Bottleneck queue manager
3. Create prompt file structure and loader
4. Build reference normalizer and resolver
5. Implement core obligation extraction
6. Add sub-element analysis
7. Create review flag system
8. Build human review interface

This architecture ensures efficient, accurate extraction while maintaining clarity about what needs human review.
