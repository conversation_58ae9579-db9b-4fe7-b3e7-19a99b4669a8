# Contract Obligation Extraction System - High Level Plan

## Overview
A full-stack application that extracts structured obligations from contract clauses using LLM-powered analysis and displays them in an intuitive side-by-side interface with interactive knowledge graph visualization.

## Database Architecture - MongoDB

**Note on Data Structure**: The obligation data structure has been updated to nest `conditions`, `events`, `temporalExpressions`, and `references` under each individual `obligation` key, rather than having them at the top level. Additionally, each obligation now includes a `relevantParty` field to identify the responsible party. The system also adds a `fullContractClause` field to the final JSON output programmatically (not via LLM) to preserve the original clause text. This change better represents the relationships between obligations and their modifying elements, ensuring each obligation is a complete, self-contained unit with its associated metadata.

### Collections Design

#### 1. `contracts` Collection
```typescript
{
  _id: ObjectId,
  title: string,
  uploadDate: Date,
  fileHash: string,
  metadata: {
    parties: string[],
    documentType: string,
    jurisdiction: string
  },
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed',
  clauses: ObjectId[] // References to clause documents
}
```

#### 2. `clauses` Collection
```typescript
{
  _id: ObjectId,
  contractId: ObjectId,
  clauseNumber: string, // e.g., "4.2(b)"
  originalText: string,
  fullContractClause: string, // Added programmatically after LLM processing
  extractedData: {
    definitions: Array<{term: string, definition: string}>,
    parties: Array<{name: string, role: string}>,
    obligationSets: Array<{
      obligation: {
        statement: string,
        modality: 'DUTY' | 'RIGHT' | 'PROHIBITION',
        relevantParty: string,
        startingQuote: string,
        endingQuote: string,
        conditions: Array<{statement: string}>,
        events: Array<{name: string}>,
        temporalExpressions: Array<{expression: string}>,
        references: Array<{pointer: string}>
      }
    }>,
    amendment: string[]
  },
  // Graph relationships stored as embedded documents
  relationships: {
    references: Array<{
      targetClauseId: ObjectId,
      pointer: string,
      type: 'cross_reference' | 'definition' | 'amendment'
    }>,
    dependencies: Array<{
      dependsOnClauseId: ObjectId,
      relationship: 'conditional' | 'temporal' | 'prerequisite'
    }>
  }
}
```

#### 3. `obligations` Collection (Denormalized for efficient querying)
```typescript
{
  _id: ObjectId,
  contractId: ObjectId,
  clauseId: ObjectId, // Reference to clauses collection for fullContractClause text
  obligation: {
    statement: string,
    modality: 'DUTY' | 'RIGHT' | 'PROHIBITION',
    relevantParty: string,
    startingQuote: string,
    endingQuote: string,
    conditions: Array<{statement: string}>,
    events: Array<{name: string}>,
    temporalExpressions: Array<{expression: string, parsedDate?: Date}>,
    references: Array<{pointer: string, targetClauseId?: ObjectId}>
  },
  // Graph-like fields for queries
  parties: string[], // Extracted parties involved
  tags: string[], // Auto-generated tags for categorization
  priority: number // Calculated priority score
}
```

### Graph Queries in MongoDB
Use MongoDB's aggregation pipeline for graph-like queries:
- `$lookup` for joins across collections
- `$graphLookup` for recursive relationship traversal
- `$match` and `$project` for filtering and shaping results

#### Database Indexes for Performance
```javascript
// Essential indexes for graph queries
db.clauses.createIndex({"relationships.references.targetClauseId": 1})
db.clauses.createIndex({"relationships.dependencies.dependsOnClauseId": 1})
db.clauses.createIndex({"contractId": 1, "clauseNumber": 1})
db.clauses.createIndex({"extractedData.definitions.term": "text"})
db.obligations.createIndex({"clauseId": 1, "contractId": 1})
// New indexes for nested obligation structure
db.clauses.createIndex({"extractedData.obligationSets.obligation.conditions.statement": "text"})
db.clauses.createIndex({"extractedData.obligationSets.obligation.temporalExpressions.expression": 1})
db.clauses.createIndex({"extractedData.obligationSets.obligation.references.pointer": "text"})
db.clauses.createIndex({"extractedData.obligationSets.obligation.relevantParty": 1})
db.clauses.createIndex({"extractedData.obligationSets.obligation.startingQuote": "text"})
db.clauses.createIndex({"extractedData.obligationSets.obligation.endingQuote": "text"})
db.obligations.createIndex({"obligation.conditions.statement": "text"})
db.obligations.createIndex({"obligation.temporalExpressions.expression": 1})
db.obligations.createIndex({"obligation.relevantParty": 1})
db.obligations.createIndex({"obligation.startingQuote": "text"})
db.obligations.createIndex({"obligation.endingQuote": "text"})
```

#### Advanced Query Patterns for Clause Relationships

##### 1. Find All Referenced Clauses (Direct References)
```javascript
db.clauses.aggregate([
  {$match: {_id: ObjectId("target_clause_id")}},
  {$lookup: {
    from: "clauses",
    localField: "relationships.references.targetClauseId",
    foreignField: "_id",
    as: "referencedClauses"
  }},
  {$project: {
    clauseNumber: 1,
    originalText: 1,
    "referencedClauses.clauseNumber": 1,
    "referencedClauses.originalText": 1,
    "referencedClauses._id": 1
  }}
])
```

##### 2. Find All Clauses That Reference This Clause (Bidirectional)
```javascript
db.clauses.aggregate([
  {$match: {"relationships.references.targetClauseId": ObjectId("target_clause_id")}},
  {$lookup: {
    from: "clauses",
    localField: "relationships.references.targetClauseId",
    foreignField: "_id",
    as: "targetClause"
  }},
  {$project: {
    clauseNumber: 1,
    originalText: 1,
    referenceType: "$relationships.references.type",
    referencePointer: "$relationships.references.pointer",
    "targetClause.clauseNumber": 1
  }}
])
```

##### 3. Recursive Reference Chain Discovery
```javascript
db.clauses.aggregate([
  {$match: {_id: ObjectId("starting_clause_id")}},
  {$graphLookup: {
    from: "clauses",
    startWith: "$relationships.references.targetClauseId",
    connectFromField: "relationships.references.targetClauseId",
    connectToField: "_id",
    as: "referenceChain",
    maxDepth: 5,
    depthField: "depth"
  }},
  {$project: {
    clauseNumber: 1,
    originalText: 1,
    "referenceChain.clauseNumber": 1,
    "referenceChain.depth": 1,
    "referenceChain._id": 1
  }}
])
```

##### 4. Definition Resolution Across Contract
```javascript
db.clauses.aggregate([
  {$match: {contractId: ObjectId("contract_id")}},
  {$unwind: "$extractedData.definitions"},
  {$match: {"extractedData.definitions.term": {$regex: "searchTerm", $options: "i"}}},
  {$group: {
    _id: "$extractedData.definitions.term",
    definition: {$first: "$extractedData.definitions.definition"},
    sourceClauses: {$push: {
      clauseId: "$_id", 
      clauseNumber: "$clauseNumber",
      context: {$substr: ["$originalText", 0, 100]}
    }}
  }},
  {$project: {
    term: "$_id",
    definition: 1,
    sourceClauses: 1,
    occurrenceCount: {$size: "$sourceClauses"}
  }}
])
```

##### 5. Dependency Chain Analysis
```javascript
db.clauses.aggregate([
  {$match: {_id: ObjectId("clause_id")}},
  {$graphLookup: {
    from: "clauses",
    startWith: "$relationships.dependencies.dependsOnClauseId",
    connectFromField: "relationships.dependencies.dependsOnClauseId",
    connectToField: "_id",
    as: "dependencyChain",
    maxDepth: 10,
    depthField: "dependencyLevel",
    restrictSearchWithMatch: {
      "relationships.dependencies": {$exists: true, $ne: []}
    }
  }},
  {$addFields: {
    totalDependencies: {$size: "$dependencyChain"},
    maxDependencyDepth: {$max: "$dependencyChain.dependencyLevel"}
  }}
])
```

##### 6. Cross-Reference Network for Visualization
```javascript
db.clauses.aggregate([
  {$match: {contractId: ObjectId("contract_id")}},
  {$project: {
    _id: 1,
    clauseNumber: 1,
    extractedData: 1,
    references: "$relationships.references.targetClauseId",
    dependencies: "$relationships.dependencies.dependsOnClauseId",
    // Extract nested references from obligations
    obligationReferences: {
      $reduce: {
        input: "$extractedData.obligationSets",
        initialValue: [],
        in: {$concatArrays: ["$$value", "$$this.obligation.references"]}
      }
    }
  }},
  {$lookup: {
    from: "clauses",
    localField: "references",
    foreignField: "_id",
    as: "referencedClauses"
  }},
  {$lookup: {
    from: "clauses",
    localField: "dependencies",
    foreignField: "_id",
    as: "dependentClauses"
  }},
  {$project: {
    node: {
      id: "$_id",
      label: "$clauseNumber",
      obligationCount: {$size: "$extractedData.obligationSets"},
      definitionCount: {$size: "$extractedData.definitions"},
      // Count total nested elements across all obligations
      totalConditions: {
        $sum: {
          $map: {
            input: "$extractedData.obligationSets",
            as: "obligationSet",
            in: {$size: "$$obligationSet.obligation.conditions"}
          }
        }
      },
      totalTemporalExpressions: {
        $sum: {
          $map: {
            input: "$extractedData.obligationSets",
            as: "obligationSet", 
            in: {$size: "$$obligationSet.obligation.temporalExpressions"}
          }
        }
      },
      // Include quote data for text highlighting
      obligationQuotes: {
        $map: {
          input: "$extractedData.obligationSets",
          as: "obligationSet",
          in: {
            startingQuote: "$$obligationSet.obligation.startingQuote",
            endingQuote: "$$obligationSet.obligation.endingQuote"
          }
        }
      }
    },
    edges: {
      $concatArrays: [
        {$map: {
          input: "$referencedClauses",
          as: "ref",
          in: {source: "$_id", target: "$$ref._id", type: "reference"}
        }},
        {$map: {
          input: "$dependentClauses",
          as: "dep",
          in: {source: "$_id", target: "$$dep._id", type: "dependency"}
        }}
      ]
    }
  }}
])
```

##### 7. Circular Reference Detection
```javascript
db.clauses.aggregate([
  {$match: {contractId: ObjectId("contract_id")}},
  {$graphLookup: {
    from: "clauses",
    startWith: "$_id",
    connectFromField: "relationships.references.targetClauseId",
    connectToField: "_id",
    as: "referenceLoop",
    maxDepth: 20,
    restrictSearchWithMatch: {
      "_id": {$ne: "$$ROOT._id"}
    }
  }},
  {$match: {
    $expr: {
      $gt: [{$size: {$filter: {
        input: "$referenceLoop",
        cond: {$eq: ["$$this._id", "$_id"]}
      }}}, 0]
    }
  }},
  {$project: {
    clauseNumber: 1,
    circularReferences: {
      $filter: {
        input: "$referenceLoop",
        cond: {$eq: ["$$this._id", "$_id"]}
      }
    }
  }}
])
```

## API Endpoints Specification

### 1. Contract Management Endpoints

#### `POST /api/contracts/upload`
- **Purpose**: Upload and initiate processing of contract documents
- **Input**: Multipart file upload (PDF, DOCX, TXT)
- **Response**: Contract ID and processing status
- **Actions**: 
  - Store file metadata
  - Extract text from document
  - Queue for LLM processing

#### `GET /api/contracts/:id`
- **Purpose**: Retrieve contract metadata and processing status
- **Response**: Contract details with clause count and processing progress

#### `GET /api/contracts`
- **Purpose**: List all contracts with filtering/pagination
- **Query Params**: `page`, `limit`, `status`, `uploadDate`

### 2. Clause Processing Endpoints

#### `POST /api/contracts/:id/process`
- **Purpose**: Trigger LLM processing for contract clauses
- **Actions**:
  - Split contract into clauses
  - Send each clause to Gemini for extraction
  - Store extracted data in `clauses` collection
  - Build cross-references between clauses

#### `GET /api/contracts/:id/clauses`
- **Purpose**: Retrieve all clauses for a contract
- **Response**: Array of clause objects with extracted data

#### `GET /api/clauses/:id`
- **Purpose**: Get detailed clause information
- **Response**: Complete clause data including obligations and relationships

### 3. Obligation Query Endpoints

#### `GET /api/contracts/:id/obligations`
- **Purpose**: Retrieve all obligations for a contract
- **Query Params**: 
  - `modality`: Filter by DUTY/RIGHT/PROHIBITION
  - `relevantParty`: Filter by relevant party
  - `hasConditions`: Boolean filter (checks nested obligation.conditions)
  - `hasTemporalExpressions`: Boolean filter (checks nested obligation.temporalExpressions)
  - `hasEvents`: Boolean filter (checks nested obligation.events)
  - `hasReferences`: Boolean filter (checks nested obligation.references)
- **Response**: Paginated list of obligations with nested structure including startingQuote and endingQuote for text highlighting

#### `GET /api/obligations/:id`
- **Purpose**: Get detailed obligation information
- **Response**: Complete obligation with related clause context including startingQuote and endingQuote for text highlighting

#### `POST /api/obligations/search`
- **Purpose**: Advanced search across obligations
- **Input**: 
```typescript
{
  query: string, // Text search
  filters: {
    modality?: string[],
    relevantParties?: string[],
    dateRange?: {start: Date, end: Date},
    tags?: string[]
  },
  contractIds?: ObjectId[]
}
```

### 4. Graph/Relationship Endpoints

#### `GET /api/contracts/:id/graph`
- **Purpose**: Get graph data for visualization
- **Response**: Nodes (clauses) and edges (relationships) for D3.js
- **Query Params**: `depth` (traversal depth), `type` (relationship types)

#### `GET /api/clauses/:id/references`
- **Purpose**: Get all clauses referenced by or referencing this clause
- **Response**: Array of related clauses with relationship types

#### `GET /api/clauses/:id/dependencies`
- **Purpose**: Get dependency chain for a clause
- **Response**: Hierarchical structure of dependent clauses

### 5. Analysis Endpoints

#### `GET /api/contracts/:id/analytics`
- **Purpose**: Contract analysis dashboard data
- **Response**: 
```typescript
{
  obligationCounts: {duties: number, rights: number, prohibitions: number},
  partyAnalysis: Array<{party: string, obligationCount: number}>,
  relevantPartyAnalysis: Array<{relevantParty: string, obligationCount: number}>,
  temporalAnalysis: Array<{date: Date, obligations: number}>,
  complexityScore: number,
  riskFactors: string[]
}
```

#### `GET /api/contracts/:id/timeline`
- **Purpose**: Temporal timeline of all obligations
- **Response**: Chronologically ordered obligations with dates extracted from nested obligation.temporalExpressions

### 6. Export Endpoints

#### `GET /api/contracts/:id/export`
- **Purpose**: Export contract analysis in various formats
- **Query Params**: `format` (json, pdf, csv, cypher)
- **Response**: Formatted export file

#### `POST /api/contracts/:id/annotations`
- **Purpose**: Add user annotations to clauses/obligations
- **Input**: Annotation data with user ID and content

## MongoDB Graph Query Examples

### Find all clauses that reference a specific clause:
```javascript
db.clauses.aggregate([
  {$match: {"relationships.references.targetClauseId": ObjectId("...)}},
  {$lookup: {from: "clauses", localField: "relationships.references.targetClauseId", foreignField: "_id", as: "referencedClauses"}}
])
```

### Get obligation dependency tree:
```javascript
db.clauses.aggregate([
  {$match: {_id: ObjectId("...")}},
  {$graphLookup: {
    from: "clauses",
    startWith: "$relationships.dependencies.dependsOnClauseId",
    connectFromField: "relationships.dependencies.dependsOnClauseId",
    connectToField: "_id",
    as: "dependencyTree"
  }}
])
```

## Implementation Plan

### Phase 1: Database & Core API
1. **MongoDB Schema Setup**
   - Design collections: `contracts`, `clauses`, `obligations`
   - Create indexes for efficient graph-like queries
   - Set up aggregation pipelines for relationship traversal

2. **Core API Development**
   - Contract upload/processing endpoints
   - Clause extraction and storage
   - Basic CRUD operations for all entities

### Phase 2: Graph Functionality
1. **Relationship Building**
   - Cross-reference detection between clauses
   - Dependency mapping using MongoDB's `$graphLookup`
   - Reference resolution and validation

2. **Advanced Query Endpoints**
   - Graph visualization data API (updated for nested obligation structure with quote data)
   - Timeline generation for temporal expressions (accessing nested obligation.temporalExpressions)
   - Analytics dashboard endpoints (aggregating across nested obligation data)
   - Text highlighting API using startingQuote and endingQuote fields

### Phase 3: Frontend Integration
1. **API Integration**
   - React components consuming the 13 specified endpoints
   - Real-time updates for processing status
   - Graph visualization using D3.js with MongoDB data
   - Text highlighting components using startingQuote and endingQuote data

2. **Export & Analysis Features**
   - Multi-format export functionality
   - User annotation system
   - Advanced search and filtering

## Technical Stack

### Backend
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js or Fastify
- **Database**: MongoDB with aggregation pipelines
- **AI Integration**: Gemini AI (existing)
- **Queue System**: Redis/Bull for processing

### Frontend
- **Framework**: React with Next.js
- **UI Library**: Tailwind CSS + Headless UI
- **State Management**: Zustand or React Query
- **Visualization**: D3.js for graphs, React-PDF for documents

## Success Metrics
- **Accuracy**: >95% obligation extraction accuracy
- **Performance**: <5s processing time per clause
- **Usability**: <30s to find specific obligation in complex contract
- **Scalability**: Handle 100+ page contracts efficiently