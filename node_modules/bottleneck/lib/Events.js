"use strict";

function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

var Events;
Events = class Events {
  constructor(instance) {
    this.instance = instance;
    this._events = {};

    if (this.instance.on != null || this.instance.once != null || this.instance.removeAllListeners != null) {
      throw new Error("An Emitter already exists for this object");
    }

    this.instance.on = (name, cb) => {
      return this._addListener(name, "many", cb);
    };

    this.instance.once = (name, cb) => {
      return this._addListener(name, "once", cb);
    };

    this.instance.removeAllListeners = (name = null) => {
      if (name != null) {
        return delete this._events[name];
      } else {
        return this._events = {};
      }
    };
  }

  _addListener(name, status, cb) {
    var base;

    if ((base = this._events)[name] == null) {
      base[name] = [];
    }

    this._events[name].push({
      cb,
      status
    });

    return this.instance;
  }

  listenerCount(name) {
    if (this._events[name] != null) {
      return this._events[name].length;
    } else {
      return 0;
    }
  }

  trigger(name, ...args) {
    var _this = this;

    return _asyncToGenerator(function* () {
      var e, promises;

      try {
        if (name !== "debug") {
          _this.trigger("debug", `Event triggered: ${name}`, args);
        }

        if (_this._events[name] == null) {
          return;
        }

        _this._events[name] = _this._events[name].filter(function (listener) {
          return listener.status !== "none";
        });
        promises = _this._events[name].map(
        /*#__PURE__*/
        function () {
          var _ref = _asyncToGenerator(function* (listener) {
            var e, returned;

            if (listener.status === "none") {
              return;
            }

            if (listener.status === "once") {
              listener.status = "none";
            }

            try {
              returned = typeof listener.cb === "function" ? listener.cb(...args) : void 0;

              if (typeof (returned != null ? returned.then : void 0) === "function") {
                return yield returned;
              } else {
                return returned;
              }
            } catch (error) {
              e = error;

              if ("name" !== "error") {
                _this.trigger("error", e);
              }

              return null;
            }
          });

          return function (_x) {
            return _ref.apply(this, arguments);
          };
        }());
        return (yield Promise.all(promises)).find(function (x) {
          return x != null;
        });
      } catch (error) {
        e = error;

        if ("name" !== "error") {
          _this.trigger("error", e);
        }

        return null;
      }
    })();
  }

};
module.exports = Events;