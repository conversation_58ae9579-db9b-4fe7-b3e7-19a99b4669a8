{"name": "bottleneck", "main": "bottleneck.js", "version": "2.19.5", "homepage": "https://github.com/SGrondin/bottleneck", "authors": ["SGrondin <<EMAIL>>"], "description": "Distributed task scheduler and rate limiter", "moduleType": ["globals", "node"], "keywords": ["async", "rate", "limiter", "limiting", "throttle", "throttling", "load", "ddos"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components"]}