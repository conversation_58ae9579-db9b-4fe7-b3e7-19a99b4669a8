import { GoogleGenAI } from '@google/genai';
import { readFileSync, writeFileSync } from 'fs';
import 'dotenv/config';

const apiKey = process.env.GEMINI_API_KEY;
if (!apiKey) {
  throw new Error('GEMINI_API_KEY environment variable is required');
}

const ai = new GoogleGenAI({ apiKey });

async function callGemini(prompt: string): Promise<string> {
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: prompt
    });
    
    return response.text || '';
  } catch (error) {
    console.error('Error calling Gemini:', error);
    throw error;
  }
}

function createLegalOntologyPrompt(contractClause: string): string {
  return `You are an expert legal AI assistant specializing in contract analysis and knowledge graph extraction. Your primary task is to meticulously analyze the contract clause provided below and extract key information according to a specific ontology.

1. Contract Clause for Analysis
Provide the single, specific contract clause to be analyzed here:
${contractClause}

2. Ontology Definitions
Extract the following items based on these precise definitions. Your primary goal is to group each Obligation with the Conditions, Events, Temporal Expressions, and References that directly apply to it.

Clause: The full, unique identifier for the clause, including any numbers and letters (e.g., "4.1(b)"). This is considered global information for the entire clause.

Definition: Every term that is capitalized, in quotes, or specially formatted. For each, capture the term and its definition if explicitly provided. These are considered global information for the entire clause.

Party: Any legal or natural person mentioned. Identify their name and role. This list should contain all parties mentioned in the clause and is considered global information.

Obligation: A normalized statement of a duty, right, or prohibition. Each obligation is the core of an "Obligation Set".
- statement: A concise, normalized description of the action.
- modality: The deontic type (DUTY, RIGHT, or PROHIBITION).
- relevantParty: The party responsible for fulfilling this obligation.
- startingQuote: First 1-8 words from the original source clause text (not the normalized statement) that supports this obligation.
- endingQuote: Last 1-8 words from the original source clause text (not the normalized statement) that supports this obligation.
- Always define the limit or boundaries of the statement.

Condition: A boolean trigger (e.g., "if X happens") that applies to a specific obligation.

Event: A contractual or real-world milestone (e.g., "Closing Date") that is linked to a specific obligation.

Temporal Expression: An absolute date or relative time period (e.g., "within 30 days") that modifies a specific obligation.

Reference: A pointer to another section (e.g., "see Section 4.2") that is relevant to a specific obligation.

Amendment: Any language that modifies a prior agreement. This is considered global information.

3. Required Output Format
Your output must be a single, valid JSON object with NO additional text, markdown formatting, or explanations. Return ONLY the JSON object. The structure must be centered around a list of ObligationSets. Each object in this list contains a single obligation object that includes the core obligation (statement and modality) along with any conditions, events, temporal expressions, or references that directly modify it nested within the same obligation object. This structure is designed to explicitly capture the relationships between these items.

If an item is not present in the clause, you may omit its key or use an empty list [].

Example Structure:
{
  "Clause": "4.2(b)",
  "Definitions": [
    {
      "term": "Initial Fee",
      "definition": "the sum of fifty thousand dollars ($50,000)"
    }
  ],
  "Parties": [
    {
      "name": "Buyer",
      "role": "Buyer"
    },
    {
      "name": "Seller",
      "role": "Seller"
    }
  ],
  "ObligationSets": [
    {
      "obligation": {
        "statement": "Buyer shall pay the Initial Fee to Seller",
        "modality": "DUTY",
        "relevantParty": "Buyer",
        "startingQuote": "Upon receipt of the Delivery",
        "endingQuote": "within ten (10) business days",
        "conditions": [],
        "events": [
          {
            "name": "receipt of the Delivery Notice"
          }
        ],
        "temporalExpressions": [
          {
            "expression": "within ten (10) business days"
          }
        ],
        "references": [
          {
            "pointer": "as defined in Exhibit A"
          }
        ]
      }
    }
  ],
  "Amendment": []
}`;
}

async function main() {
  try {
    // Read the contract clause from the markdown file
    const contractClause = readFileSync('./contract-clause.md', 'utf-8');
    
    // Create the legal ontology extraction prompt
    const prompt = createLegalOntologyPrompt(contractClause);
    
    // Call Gemini with the prompt
    const response = await callGemini(prompt);
    console.log('Legal Ontology Extraction Result:');
    console.log(response);
    
    // Extract JSON from the response (in case it's wrapped in markdown or other text)
    let jsonResponse = response;
    
    // Try to find JSON within the response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      jsonResponse = jsonMatch[0];
    }
    
    // Parse the LLM response and add the full contract clause
    const parsedResponse = JSON.parse(jsonResponse);
    parsedResponse.fullContractClause = contractClause;
    
    // Save enhanced response to result.json
    writeFileSync('./result.json', JSON.stringify(parsedResponse, null, 2), 'utf-8');
    console.log('Response saved to result.json');
  } catch (error) {
    console.error('Failed to get response:', error);
  }
}

main();

export { callGemini };