import Bottleneck from 'bottleneck';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { QueueStats, ExtractionConfig } from '../types/index.js';

/**
 * Service for managing API requests to Gemini with rate limiting and retry logic
 */
export class QueueManagerService {
  private limiter: Bottleneck;
  private genAI: GoogleGenerativeAI;
  private model: any;
  private config: ExtractionConfig;

  constructor(config: ExtractionConfig) {
    this.config = config;
    
    // Initialize Bottleneck with Gemini rate limits
    this.limiter = new Bottleneck({
      minTime: config.minTimeBetweenRequests || 1000, // Minimum time between requests (1 second)
      maxConcurrent: config.maxConcurrent || 5, // Maximum concurrent requests
      reservoir: config.requestsPerMinute || 60, // Token bucket size (requests per minute)
      reservoirRefreshAmount: config.requestsPerMinute || 60,
      reservoirRefreshInterval: 60 * 1000, // Refill every minute

      // Retry configuration
      retryDelay: config.retryDelay || 2000,
      maxRetryTime: config.maxRetryTime || 30000,
    });

    // Initialize Gemini
    this.genAI = new GoogleGenerativeAI(config.geminiApiKey);
    this.model = this.genAI.getGenerativeModel({
      model: 'gemini-2.0-flash-exp',
      generationConfig: {
        temperature: 0.2,
        maxOutputTokens: 8192,
        responseMimeType: 'application/json'
      },
    });

    // Set up error handling
    this.limiter.on('error', (error) => {
      console.error('Queue error:', error);
    });

    this.limiter.on('retry', (error, jobInfo) => {
      console.log(
        `Retrying job ${jobInfo.options.id} after error:`,
        error.message
      );
    });

    this.limiter.on('depleted', () => {
      console.log('Rate limit reservoir depleted, waiting for refill...');
    });

    this.limiter.on('debug', (message) => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('Bottleneck debug:', message);
      }
    });
  }

  /**
   * Queue a request to Gemini with automatic rate limiting
   */
  async queueRequest(
    prompt: string,
    context: string,
    priority?: number,
    jobId?: string
  ): Promise<string> {
    return this.limiter.schedule(
      { 
        priority: priority || 5, // Higher number = higher priority
        id: jobId || `job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      },
      async () => this.callGemini(prompt, context)
    );
  }

  /**
   * Make the actual call to Gemini API
   */
  private async callGemini(prompt: string, context: string): Promise<string> {
    try {
      const result = await this.model.generateContent({
        contents: [
          {
            role: 'user',
            parts: [{ text: prompt }],
          },
        ],
        systemInstruction: context,
      });

      const response = result.response;
      
      if (!response) {
        throw new Error('No response received from Gemini');
      }

      const text = response.text();
      
      if (!text) {
        throw new Error('Empty response from Gemini');
      }

      return text;
    } catch (error: any) {
      // Handle specific Gemini API errors
      if (error.status === 429) {
        // Rate limit - Bottleneck will retry
        throw new Error('RATE_LIMIT_EXCEEDED');
      } else if (error.status === 400) {
        throw new Error(`Bad request to Gemini: ${error.message}`);
      } else if (error.status === 401) {
        throw new Error('Invalid Gemini API key');
      } else if (error.status === 403) {
        throw new Error('Gemini API access forbidden');
      } else if (error.status >= 500) {
        throw new Error(`Gemini server error: ${error.message}`);
      }
      
      throw error;
    }
  }

  /**
   * Queue multiple requests with different priorities
   */
  async queueBatchRequests(requests: Array<{
    prompt: string;
    context: string;
    priority?: number;
    jobId?: string;
  }>): Promise<string[]> {
    const promises = requests.map(req => 
      this.queueRequest(req.prompt, req.context, req.priority, req.jobId)
    );
    
    return Promise.all(promises);
  }

  /**
   * Get current queue statistics
   */
  getQueueStats(): QueueStats {
    return {
      running: this.limiter.running(),
      queued: this.limiter.queued(),
      done: this.limiter.done(),
    };
  }

  /**
   * Wait for all queued jobs to complete
   */
  async waitForCompletion(): Promise<void> {
    await this.limiter.stop({ dropWaitingJobs: false });
  }

  /**
   * Clear all waiting jobs
   */
  clearQueue(): void {
    this.limiter.stop({ dropWaitingJobs: true });
  }

  /**
   * Update rate limiting configuration
   */
  updateRateLimit(config: Partial<ExtractionConfig>): void {
    if (config.requestsPerMinute) {
      this.limiter.updateSettings({
        reservoir: config.requestsPerMinute,
        reservoirRefreshAmount: config.requestsPerMinute
      });
    }
    
    if (config.maxConcurrent) {
      this.limiter.updateSettings({
        maxConcurrent: config.maxConcurrent
      });
    }
    
    if (config.minTimeBetweenRequests) {
      this.limiter.updateSettings({
        minTime: config.minTimeBetweenRequests
      });
    }
  }

  /**
   * Get detailed queue information
   */
  getDetailedStats(): {
    stats: QueueStats;
    settings: any;
    counts: any;
  } {
    return {
      stats: this.getQueueStats(),
      settings: {
        minTime: this.limiter.settings.minTime,
        maxConcurrent: this.limiter.settings.maxConcurrent,
        reservoir: this.limiter.settings.reservoir,
        reservoirRefreshAmount: this.limiter.settings.reservoirRefreshAmount,
        reservoirRefreshInterval: this.limiter.settings.reservoirRefreshInterval
      },
      counts: this.limiter.counts()
    };
  }

  /**
   * Test the connection to Gemini API
   */
  async testConnection(): Promise<boolean> {
    try {
      const testPrompt = 'Respond with "OK" if you can read this message.';
      const testContext = 'You are a test assistant. Respond briefly.';
      
      const response = await this.queueRequest(testPrompt, testContext, 10, 'connection-test');
      return response.toLowerCase().includes('ok');
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  /**
   * Gracefully shutdown the queue manager
   */
  async shutdown(): Promise<void> {
    console.log('Shutting down queue manager...');
    await this.limiter.stop({ dropWaitingJobs: false });
    console.log('Queue manager shutdown complete');
  }
}
