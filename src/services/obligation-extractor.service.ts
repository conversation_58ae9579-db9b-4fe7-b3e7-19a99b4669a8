import { 
  ClauseAnalysisResult, 
  Obligation, 
  Condition, 
  Event, 
  TemporalExpression, 
  Reference 
} from '../types/index.js';
import { KeywordSearchService } from './keyword-search.service.js';
import { QueueManagerService } from './queue-manager.service.js';
import { PromptManagerService } from './prompt-manager.service.js';
import { ReferenceResolverService } from './reference-resolver.service.js';
import { TextAnchorService } from './text-anchor.service.js';

/**
 * Main service that orchestrates the entire obligation extraction pipeline
 */
export class ObligationExtractorService {
  constructor(
    private keywordSearch: KeywordSearchService,
    private queueManager: QueueManagerService,
    private promptManager: PromptManagerService,
    private referenceResolver: ReferenceResolverService,
    private textAnchor: TextAnchorService
  ) {}

  /**
   * Extract obligations from a single clause
   * This is the main entry point for clause analysis
   */
  async extractObligationsFromClause(
    clauseText: string,
    clauseNumber?: string
  ): Promise<ClauseAnalysisResult> {
    console.log(`Analyzing clause ${clauseNumber || 'unnamed'}...`);

    try {
      // Step 1: Keyword search to determine if clause needs analysis
      const searchResult = await this.keywordSearch.findObligationClauses(clauseText);

      if (!searchResult.containsObligations) {
        return {
          clauseNumber,
          clauseText,
          containsObligations: false,
          obligations: [],
          skippedReason: 'No obligation keywords found',
        };
      }

      console.log(`Found ${searchResult.keywords.length} obligation keywords with confidence ${searchResult.confidence}`);

      // Step 2: Send full clause to Gemini for extraction
      const obligations = await this.extractWithLLM(searchResult.fullClause, clauseNumber);

      if (obligations.length === 0) {
        return {
          clauseNumber,
          clauseText: searchResult.fullClause,
          containsObligations: false,
          obligations: [],
          keywordsFound: searchResult.keywords,
          confidence: searchResult.confidence,
          skippedReason: 'LLM found no obligations despite keywords',
        };
      }

      console.log(`Extracted ${obligations.length} obligations from LLM`);

      // Step 3: Analyze sub-elements for each obligation
      const analyzedObligations = await this.analyzeObligationDetails(obligations);

      // Step 4: Resolve references and flag unresolved ones
      const finalObligations = await this.referenceResolver.resolveReferences(
        analyzedObligations,
        clauseNumber
      );

      // Step 5: Validate text anchors
      const validatedObligations = this.validateTextAnchors(finalObligations, searchResult.fullClause);

      return {
        clauseNumber,
        clauseText: searchResult.fullClause,
        containsObligations: true,
        obligations: validatedObligations,
        keywordsFound: searchResult.keywords,
        confidence: searchResult.confidence,
      };
    } catch (error) {
      console.error('Error extracting obligations:', error);
      return {
        clauseNumber,
        clauseText,
        containsObligations: false,
        obligations: [],
        skippedReason: `Extraction failed: ${error}`,
      };
    }
  }

  /**
   * Extract obligations using LLM
   */
  private async extractWithLLM(clauseText: string, clauseNumber?: string): Promise<Obligation[]> {
    // Load prompt from file
    const prompt = await this.promptManager.loadPrompt('extraction/find-obligations.md', {
      CLAUSE_TEXT: clauseText,
      CLAUSE_NUMBER: clauseNumber || 'N/A',
    });

    const context = this.promptManager.getSystemContext('extraction');

    try {
      const response = await this.queueManager.queueRequest(prompt, context, 1);
      const obligations = JSON.parse(response);

      return obligations.map((obl: any, index: number) => ({
        id: `OBL-${Date.now()}-${index}`,
        clauseNumber,
        text: obl.text,
        type: obl.type || 'obligation',
        party: obl.party,
        action: obl.action,
        startingQuote: obl.startingQuote || '',
        endingQuote: obl.endingQuote || '',
        conditions: [],
        events: [],
        temporalExpressions: [],
        references: [],
        validationStatus: 'pending' as const,
        needsReview: false,
      }));
    } catch (error) {
      console.error('LLM extraction error:', error);
      throw error;
    }
  }

  /**
   * Analyze detailed sub-elements of obligations
   */
  private async analyzeObligationDetails(obligations: Obligation[]): Promise<Obligation[]> {
    // Process obligations in parallel with rate limiting handled by queue
    const analysisPromises = obligations.map(async (obligation) => {
      const [conditions, events, temporal, references] = await Promise.all([
        this.extractConditions(obligation),
        this.extractEvents(obligation),
        this.extractTemporalExpressions(obligation),
        this.extractReferences(obligation),
      ]);

      return {
        ...obligation,
        conditions,
        events,
        temporalExpressions: temporal,
        references,
      };
    });

    return Promise.all(analysisPromises);
  }

  /**
   * Extract conditions from obligation
   */
  private async extractConditions(obligation: Obligation): Promise<Condition[]> {
    try {
      const prompt = await this.promptManager.loadPrompt('analysis/extract-conditions.md', {
        OBLIGATION_TEXT: obligation.text,
      });

      const response = await this.queueManager.queueRequest(
        prompt,
        this.promptManager.getSystemContext('analysis'),
        2
      );

      return JSON.parse(response);
    } catch (error) {
      console.error('Error extracting conditions:', error);
      return [];
    }
  }

  /**
   * Extract events from obligation
   */
  private async extractEvents(obligation: Obligation): Promise<Event[]> {
    try {
      const prompt = await this.promptManager.loadPrompt('analysis/extract-events.md', {
        OBLIGATION_TEXT: obligation.text,
      });

      const response = await this.queueManager.queueRequest(
        prompt,
        this.promptManager.getSystemContext('analysis'),
        2
      );

      return JSON.parse(response);
    } catch (error) {
      console.error('Error extracting events:', error);
      return [];
    }
  }

  /**
   * Extract temporal expressions from obligation
   */
  private async extractTemporalExpressions(obligation: Obligation): Promise<TemporalExpression[]> {
    try {
      const prompt = await this.promptManager.loadPrompt('analysis/extract-temporal.md', {
        OBLIGATION_TEXT: obligation.text,
      });

      const response = await this.queueManager.queueRequest(
        prompt,
        this.promptManager.getSystemContext('analysis'),
        2
      );

      return JSON.parse(response);
    } catch (error) {
      console.error('Error extracting temporal expressions:', error);
      return [];
    }
  }

  /**
   * Extract references from obligation
   */
  private async extractReferences(obligation: Obligation): Promise<Reference[]> {
    try {
      const prompt = await this.promptManager.loadPrompt('analysis/extract-references.md', {
        OBLIGATION_TEXT: obligation.text,
      });

      const response = await this.queueManager.queueRequest(
        prompt,
        this.promptManager.getSystemContext('analysis'),
        2
      );

      return JSON.parse(response);
    } catch (error) {
      console.error('Error extracting references:', error);
      return [];
    }
  }

  /**
   * Validate and improve text anchors
   */
  private validateTextAnchors(obligations: Obligation[], clauseText: string): Obligation[] {
    return obligations.map(obligation => {
      const validation = this.textAnchor.validateTextAnchors(obligation, clauseText);
      
      if (!validation.isValid && validation.suggestions) {
        // Use suggested anchors if current ones are invalid
        return {
          ...obligation,
          startingQuote: validation.suggestions.startingQuote,
          endingQuote: validation.suggestions.endingQuote,
          needsReview: true,
        };
      }

      return {
        ...obligation,
        needsReview: obligation.needsReview || !validation.isValid,
      };
    });
  }

  /**
   * Process multiple clauses in batch
   */
  async extractObligationsFromClauses(
    clauses: Array<{ text: string; number?: string }>
  ): Promise<ClauseAnalysisResult[]> {
    const results: ClauseAnalysisResult[] = [];

    for (const clause of clauses) {
      const result = await this.extractObligationsFromClause(clause.text, clause.number);
      results.push(result);
    }

    return results;
  }

  /**
   * Get extraction statistics
   */
  getExtractionStats(results: ClauseAnalysisResult[]): {
    totalClauses: number;
    clausesWithObligations: number;
    totalObligations: number;
    averageObligationsPerClause: number;
    averageConfidence: number;
    clausesNeedingReview: number;
  } {
    const totalClauses = results.length;
    const clausesWithObligations = results.filter(r => r.containsObligations).length;
    const totalObligations = results.reduce((sum, r) => sum + r.obligations.length, 0);
    const totalConfidence = results.reduce((sum, r) => sum + (r.confidence || 0), 0);
    const clausesNeedingReview = results.filter(r => 
      r.obligations.some(o => o.needsReview)
    ).length;

    return {
      totalClauses,
      clausesWithObligations,
      totalObligations,
      averageObligationsPerClause: totalClauses > 0 ? totalObligations / totalClauses : 0,
      averageConfidence: totalClauses > 0 ? totalConfidence / totalClauses : 0,
      clausesNeedingReview,
    };
  }
}
