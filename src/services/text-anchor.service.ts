import { 
  Obligation, 
  ExtractedQuote, 
  TextHighlight, 
  HighlightedClause 
} from '../types/index.js';

/**
 * Service for extracting supporting quotes using text anchors and generating highlighted HTML
 */
export class TextAnchorService {
  
  /**
   * Extract the full supporting quote for an obligation using start/end anchors
   */
  extractSupportingQuote(
    clauseText: string,
    startingQuote: string,
    endingQuote: string
  ): ExtractedQuote | null {
    try {
      // Escape special regex characters in quotes
      const escapedStart = this.escapeRegex(startingQuote);
      const escapedEnd = this.escapeRegex(endingQuote);

      // Build regex pattern with non-greedy matching
      const pattern = new RegExp(
        `(${escapedStart}.*?${escapedEnd})`,
        'is' // case-insensitive, dotAll
      );

      const match = clauseText.match(pattern);

      if (match && match[1]) {
        const extractedText = match[1].trim();
        const startIndex = clauseText.indexOf(match[1]);

        return {
          text: extractedText,
          startIndex,
          endIndex: startIndex + extractedText.length,
          confidence: this.calculateExtractionConfidence(
            startingQuote,
            endingQuote,
            extractedText
          ),
        };
      }

      return null;
    } catch (error) {
      console.error('Regex extraction failed:', error);
      return null;
    }
  }

  /**
   * Generate optimal starting and ending quotes from obligation text
   * This can be used to validate/improve LLM-generated quotes
   */
  generateOptimalQuotes(
    obligationText: string,
    clauseText: string
  ): { startingQuote: string; endingQuote: string } {
    const words = obligationText.trim().split(/\s+/);

    // Try different lengths starting from 3 words
    for (let startLen = 3; startLen <= Math.min(8, words.length); startLen++) {
      for (let endLen = 3; endLen <= Math.min(8, words.length); endLen++) {
        const startQuote = words.slice(0, startLen).join(' ');
        const endQuote = words.slice(-endLen).join(' ');

        // Test if these quotes uniquely identify the obligation
        if (this.testQuoteUniqueness(clauseText, startQuote, endQuote)) {
          return { startingQuote: startQuote, endingQuote: endQuote };
        }
      }
    }

    // Fallback to longer quotes if needed
    return {
      startingQuote: words.slice(0, Math.min(8, words.length)).join(' '),
      endingQuote: words.slice(-Math.min(8, words.length)).join(' '),
    };
  }

  /**
   * Test if start/end quotes uniquely identify a single text span
   */
  private testQuoteUniqueness(
    clauseText: string,
    startQuote: string,
    endQuote: string
  ): boolean {
    const escapedStart = this.escapeRegex(startQuote);
    const escapedEnd = this.escapeRegex(endQuote);
    const pattern = new RegExp(`${escapedStart}.*?${escapedEnd}`, 'gis');

    const matches = clauseText.match(pattern);
    return matches !== null && matches.length === 1;
  }

  /**
   * Escape special regex characters
   */
  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Calculate confidence score for text extraction
   */
  private calculateExtractionConfidence(
    startQuote: string,
    endQuote: string,
    extractedText: string
  ): number {
    let confidence = 1.0;

    // Reduce confidence for very short quotes (less reliable)
    if (startQuote.split(' ').length < 3) confidence -= 0.2;
    if (endQuote.split(' ').length < 3) confidence -= 0.2;

    // Reduce confidence for very long extractions (might include extra text)
    if (extractedText.length > 500) confidence -= 0.1;

    // Check if extracted text actually starts/ends with the quotes
    if (!extractedText.toLowerCase().startsWith(startQuote.toLowerCase())) {
      confidence -= 0.3;
    }
    if (!extractedText.toLowerCase().endsWith(endQuote.toLowerCase())) {
      confidence -= 0.3;
    }

    return Math.max(0, confidence);
  }

  /**
   * Highlight obligation text within a clause for UI display
   */
  highlightObligationInClause(
    clauseText: string,
    obligations: Obligation[]
  ): HighlightedClause {
    const highlights: TextHighlight[] = [];

    obligations.forEach((obligation, index) => {
      const extracted = this.extractSupportingQuote(
        clauseText,
        obligation.startingQuote,
        obligation.endingQuote
      );

      if (extracted) {
        highlights.push({
          obligationId: obligation.id,
          startIndex: extracted.startIndex,
          endIndex: extracted.endIndex,
          text: extracted.text,
          cssClass: `obligation-highlight-${index}`,
          confidence: extracted.confidence,
        });
      }
    });

    // Sort highlights by start position
    highlights.sort((a, b) => a.startIndex - b.startIndex);

    return {
      originalText: clauseText,
      highlights,
      highlightedHtml: this.generateHighlightedHtml(clauseText, highlights),
    };
  }

  /**
   * Generate HTML with highlighted obligations
   */
  private generateHighlightedHtml(
    text: string,
    highlights: TextHighlight[]
  ): string {
    let html = '';
    let lastIndex = 0;

    highlights.forEach((highlight) => {
      // Add text before highlight
      html += this.escapeHtml(text.substring(lastIndex, highlight.startIndex));

      // Add highlighted text
      html += `<span class="${highlight.cssClass}" data-obligation-id="${highlight.obligationId}" data-confidence="${highlight.confidence}">`;
      html += this.escapeHtml(highlight.text);
      html += '</span>';

      lastIndex = highlight.endIndex;
    });

    // Add remaining text
    html += this.escapeHtml(text.substring(lastIndex));

    return html;
  }

  /**
   * Escape HTML characters
   */
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * Validate text anchors for an obligation
   */
  validateTextAnchors(
    obligation: Obligation,
    clauseText: string
  ): {
    isValid: boolean;
    issues: string[];
    suggestions: { startingQuote: string; endingQuote: string } | null;
  } {
    const issues: string[] = [];
    let isValid = true;

    // Test current anchors
    const extracted = this.extractSupportingQuote(
      clauseText,
      obligation.startingQuote,
      obligation.endingQuote
    );

    if (!extracted) {
      issues.push('Text anchors do not match any text in the clause');
      isValid = false;
    } else {
      if (extracted.confidence < 0.7) {
        issues.push('Low confidence text extraction');
        isValid = false;
      }

      // Check if the extracted text contains the obligation text
      if (!extracted.text.toLowerCase().includes(obligation.text.toLowerCase().substring(0, 50))) {
        issues.push('Extracted text does not seem to contain the obligation');
        isValid = false;
      }
    }

    // Generate suggestions if there are issues
    let suggestions = null;
    if (!isValid) {
      suggestions = this.generateOptimalQuotes(obligation.text, clauseText);
    }

    return { isValid, issues, suggestions };
  }

  /**
   * Find overlapping highlights and resolve conflicts
   */
  resolveHighlightConflicts(highlights: TextHighlight[]): TextHighlight[] {
    // Sort by start position
    const sorted = [...highlights].sort((a, b) => a.startIndex - b.startIndex);
    const resolved: TextHighlight[] = [];

    for (let i = 0; i < sorted.length; i++) {
      const current = sorted[i];
      let hasOverlap = false;

      // Check for overlaps with already resolved highlights
      for (const existing of resolved) {
        if (this.hasOverlap(current, existing)) {
          hasOverlap = true;
          // Keep the one with higher confidence
          if (current.confidence > existing.confidence) {
            // Replace existing with current
            const index = resolved.indexOf(existing);
            resolved[index] = current;
          }
          break;
        }
      }

      if (!hasOverlap) {
        resolved.push(current);
      }
    }

    return resolved.sort((a, b) => a.startIndex - b.startIndex);
  }

  /**
   * Check if two highlights overlap
   */
  private hasOverlap(a: TextHighlight, b: TextHighlight): boolean {
    return !(a.endIndex <= b.startIndex || b.endIndex <= a.startIndex);
  }

  /**
   * Generate CSS styles for obligation highlighting
   */
  generateHighlightCSS(): string {
    return `
      .obligation-highlight-0 { background-color: #ffeb3b; border-left: 3px solid #fbc02d; }
      .obligation-highlight-1 { background-color: #e1f5fe; border-left: 3px solid #0288d1; }
      .obligation-highlight-2 { background-color: #f3e5f5; border-left: 3px solid #7b1fa2; }
      .obligation-highlight-3 { background-color: #e8f5e8; border-left: 3px solid #388e3c; }
      .obligation-highlight-4 { background-color: #fff3e0; border-left: 3px solid #f57c00; }
      .obligation-highlight-5 { background-color: #fce4ec; border-left: 3px solid #c2185b; }
      
      [class*="obligation-highlight-"] {
        padding: 2px 4px;
        margin: 1px 0;
        border-radius: 3px;
        cursor: pointer;
        transition: all 0.2s ease;
      }
      
      [class*="obligation-highlight-"]:hover {
        opacity: 0.8;
        transform: scale(1.02);
      }
      
      [data-confidence="1"] { border-width: 3px; }
      [data-confidence^="0.9"] { border-width: 2px; }
      [data-confidence^="0.8"] { border-width: 2px; opacity: 0.9; }
      [data-confidence^="0.7"] { border-width: 1px; opacity: 0.8; }
      [data-confidence^="0.6"] { border-width: 1px; opacity: 0.7; }
    `;
  }

  /**
   * Extract text statistics for analysis
   */
  getTextStatistics(clauseText: string, obligations: Obligation[]): {
    totalCharacters: number;
    totalWords: number;
    highlightedCharacters: number;
    highlightedWords: number;
    coveragePercentage: number;
    averageConfidence: number;
  } {
    const totalCharacters = clauseText.length;
    const totalWords = clauseText.split(/\s+/).length;
    
    let highlightedCharacters = 0;
    let highlightedWords = 0;
    let totalConfidence = 0;
    let validExtractions = 0;

    obligations.forEach(obligation => {
      const extracted = this.extractSupportingQuote(
        clauseText,
        obligation.startingQuote,
        obligation.endingQuote
      );

      if (extracted) {
        highlightedCharacters += extracted.text.length;
        highlightedWords += extracted.text.split(/\s+/).length;
        totalConfidence += extracted.confidence;
        validExtractions++;
      }
    });

    const coveragePercentage = totalCharacters > 0 ? (highlightedCharacters / totalCharacters) * 100 : 0;
    const averageConfidence = validExtractions > 0 ? totalConfidence / validExtractions : 0;

    return {
      totalCharacters,
      totalWords,
      highlightedCharacters,
      highlightedWords,
      coveragePercentage,
      averageConfidence
    };
  }
}
