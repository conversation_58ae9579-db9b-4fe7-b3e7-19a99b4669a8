import { ClauseSearchResult } from '../types/index.js';

/**
 * Service for pre-filtering clauses using keyword search to identify
 * potential obligation-containing clauses before sending to LLM
 */
export class KeywordSearchService {
  private obligationKeywords = [
    "shall",
    "must",
    "will",
    "required to",
    "obligated to",
    "agrees to",
    "undertakes to",
    "commits to",
    "responsible for",
    "duty to",
    "covenant",
    "promise",
    "bound to",
    "liable for",
    "ensure",
    "guarantee",
    "warrant",
    "certify",
    "acknowledge",
    "confirm"
  ];

  private strongIndicators = [
    "shall",
    "must",
    "required to",
    "obligated to",
    "duty to",
    "covenant"
  ];

  private weakIndicators = [
    "will",
    "agrees to",
    "undertakes to",
    "commits to",
    "responsible for",
    "promise",
    "bound to",
    "liable for"
  ];

  /**
   * Search for clauses containing obligation keywords
   * Returns complete paragraphs/clauses for analysis
   */
  async findObligationClauses(clauseText: string): Promise<ClauseSearchResult> {
    const lowerText = clauseText.toLowerCase();

    // Check if clause contains any obligation keywords
    const foundKeywords = this.obligationKeywords.filter((keyword) =>
      lowerText.includes(keyword.toLowerCase())
    );

    if (foundKeywords.length === 0) {
      return {
        containsObligations: false,
        keywords: [],
        fullClause: "",
      };
    }

    // Extract the complete paragraph/clause
    const fullClause = this.extractCompleteParagraph(clauseText);

    return {
      containsObligations: true,
      keywords: foundKeywords,
      fullClause: fullClause,
      confidence: this.calculateKeywordConfidence(foundKeywords),
    };
  }

  /**
   * Extract complete paragraph from text
   * In a single clause context, this returns the full text
   * In a document context, this would extract the paragraph
   */
  private extractCompleteParagraph(text: string): string {
    // Return the full text as it's already a single clause
    // In a full document context, this would extract the paragraph
    return text.trim();
  }

  /**
   * Calculate confidence score based on found keywords
   */
  private calculateKeywordConfidence(keywords: string[]): number {
    let confidence = 0.3; // Base confidence for having any keywords

    // Count strong indicators
    const strongCount = keywords.filter((k) =>
      this.strongIndicators.includes(k.toLowerCase())
    ).length;

    // Count weak indicators
    const weakCount = keywords.filter((k) =>
      this.weakIndicators.includes(k.toLowerCase())
    ).length;

    // Strong indicators add more confidence
    confidence += strongCount * 0.25;
    
    // Weak indicators add less confidence
    confidence += weakCount * 0.15;

    // Multiple keywords increase confidence
    if (keywords.length > 1) {
      confidence += 0.1;
    }

    // Cap at 1.0
    return Math.min(confidence, 1.0);
  }

  /**
   * Get detailed analysis of keywords found in text
   */
  getKeywordAnalysis(clauseText: string): {
    strongIndicators: string[];
    weakIndicators: string[];
    totalKeywords: number;
    confidence: number;
  } {
    const lowerText = clauseText.toLowerCase();
    
    const foundStrong = this.strongIndicators.filter((keyword) =>
      lowerText.includes(keyword.toLowerCase())
    );
    
    const foundWeak = this.weakIndicators.filter((keyword) =>
      lowerText.includes(keyword.toLowerCase())
    );

    const allFound = [...foundStrong, ...foundWeak];

    return {
      strongIndicators: foundStrong,
      weakIndicators: foundWeak,
      totalKeywords: allFound.length,
      confidence: this.calculateKeywordConfidence(allFound)
    };
  }

  /**
   * Check if text contains specific obligation patterns
   */
  hasObligationPatterns(text: string): boolean {
    const patterns = [
      /\b(shall|must|will)\s+\w+/i,
      /\b(required|obligated|bound)\s+to\b/i,
      /\b(agrees|undertakes|commits)\s+to\b/i,
      /\b(responsible|liable)\s+for\b/i,
      /\bduty\s+to\b/i,
      /\bcovenant\b/i
    ];

    return patterns.some(pattern => pattern.test(text));
  }

  /**
   * Extract keyword context (surrounding words) for better analysis
   */
  extractKeywordContext(text: string, keyword: string, contextWords: number = 3): string[] {
    const words = text.split(/\s+/);
    const contexts: string[] = [];
    
    for (let i = 0; i < words.length; i++) {
      if (words[i].toLowerCase().includes(keyword.toLowerCase())) {
        const start = Math.max(0, i - contextWords);
        const end = Math.min(words.length, i + contextWords + 1);
        const context = words.slice(start, end).join(' ');
        contexts.push(context);
      }
    }
    
    return contexts;
  }

  /**
   * Get all obligation keywords for reference
   */
  getObligationKeywords(): string[] {
    return [...this.obligationKeywords];
  }

  /**
   * Add custom keywords to the search
   */
  addCustomKeywords(keywords: string[]): void {
    keywords.forEach(keyword => {
      if (!this.obligationKeywords.includes(keyword.toLowerCase())) {
        this.obligationKeywords.push(keyword.toLowerCase());
      }
    });
  }
}
