import { 
  Obligation, 
  Reference, 
  ResolvedReference, 
  UnresolvedReference 
} from '../types/index.js';

/**
 * Service for normalizing and resolving clause references
 * Handles variations like "4(d)" vs "4d", "Section IV" vs "4", etc.
 */
export class ReferenceResolverService {
  
  /**
   * Normalize clause references to handle variations
   * Examples: "4(d)" -> "4d", "Clause 4.1" -> "4.1", "Section IV" -> "4"
   */
  normalizeReference(reference: string): string {
    let normalized = reference.toLowerCase().trim();

    // Remove common prefixes
    normalized = normalized.replace(
      /^(clause|section|paragraph|article|subsection|part)\s*/i,
      ''
    );

    // Convert Roman numerals to Arabic
    normalized = this.convertRomanToArabic(normalized);

    // Handle parentheses: "4(d)" -> "4d"
    normalized = normalized.replace(/\(([a-z0-9]+)\)/g, '$1');

    // Remove special characters but keep alphanumeric and dots
    normalized = normalized.replace(/[^a-z0-9.]/g, '');

    return normalized;
  }

  /**
   * Create multiple possible variations of a reference
   */
  generateReferenceVariations(reference: string): string[] {
    const variations = new Set<string>();

    // Original
    variations.add(reference);

    // Normalized
    const normalized = this.normalizeReference(reference);
    variations.add(normalized);

    // With common prefixes
    const prefixes = ['clause', 'section', 'paragraph', 'article', ''];
    const baseRef = reference.replace(
      /^(clause|section|paragraph|article|subsection|part)\s*/i,
      ''
    );

    prefixes.forEach((prefix) => {
      if (prefix) {
        variations.add(`${prefix} ${baseRef}`);
        variations.add(`${prefix}${baseRef}`);
      }
    });

    // Handle parentheses variations: "4(d)" <-> "4d"
    if (reference.includes('(')) {
      variations.add(reference.replace(/[()]/g, ''));
    } else if (/\d[a-z]/.test(reference)) {
      // Add parentheses: "4d" -> "4(d)"
      variations.add(reference.replace(/(\d)([a-z])/i, '$1($2)'));
    }

    // Handle dot variations: "4.1" <-> "41"
    if (reference.includes('.')) {
      variations.add(reference.replace(/\./g, ''));
    } else if (/\d+[a-z]*\d+/.test(reference)) {
      // Add dots: "41" -> "4.1" (only if it looks like a subsection)
      const withDot = reference.replace(/(\d+)([a-z]*)(\d+)/, '$1$2.$3');
      variations.add(withDot);
    }

    // Case variations
    variations.add(reference.toLowerCase());
    variations.add(reference.toUpperCase());

    return Array.from(variations);
  }

  /**
   * Resolve references within obligations
   */
  async resolveReferences(
    obligations: Obligation[],
    currentClauseNumber?: string
  ): Promise<Obligation[]> {
    const referenceIndex = this.buildReferenceIndex(obligations);

    return obligations.map((obligation) => {
      const unresolvedReferences: UnresolvedReference[] = [];
      const resolvedReferences: ResolvedReference[] = [];

      obligation.references.forEach((ref) => {
        const variations = this.generateReferenceVariations(ref.target);
        let resolved = false;

        // Try to find a match using any variation
        for (const variation of variations) {
          const normalizedVariation = this.normalizeReference(variation);
          if (referenceIndex.has(normalizedVariation)) {
            resolvedReferences.push({
              ...ref,
              resolvedTo: referenceIndex.get(normalizedVariation)!,
              confidence: this.calculateMatchConfidence(ref.target, variation),
            });
            resolved = true;
            break;
          }
        }

        if (!resolved) {
          unresolvedReferences.push({
            ...ref,
            reason: 'No matching clause found',
            attemptedVariations: variations,
          });
        }
      });

      return {
        ...obligation,
        resolvedReferences,
        unresolvedReferences,
        needsReview: unresolvedReferences.length > 0,
      };
    });
  }

  /**
   * Build an index of available clause references
   */
  private buildReferenceIndex(obligations: Obligation[]): Map<string, string> {
    const index = new Map<string, string>();

    obligations.forEach((obl) => {
      if (obl.clauseNumber) {
        // Add normalized version
        const normalized = this.normalizeReference(obl.clauseNumber);
        index.set(normalized, obl.clauseNumber);

        // Add common variations
        const variations = this.generateReferenceVariations(obl.clauseNumber);
        variations.forEach((v) => {
          index.set(this.normalizeReference(v), obl.clauseNumber);
        });
      }
    });

    return index;
  }

  /**
   * Convert Roman numerals to Arabic numbers
   */
  private convertRomanToArabic(text: string): string {
    const romanNumerals: Record<string, number> = {
      'i': 1, 'ii': 2, 'iii': 3, 'iv': 4, 'v': 5,
      'vi': 6, 'vii': 7, 'viii': 8, 'ix': 9, 'x': 10,
      'xi': 11, 'xii': 12, 'xiii': 13, 'xiv': 14, 'xv': 15,
      'xvi': 16, 'xvii': 17, 'xviii': 18, 'xix': 19, 'xx': 20
    };

    return text.replace(/\b[ivx]+\b/g, (match) => {
      const value = romanNumerals[match];
      return value ? String(value) : match;
    });
  }

  /**
   * Calculate confidence score for reference matches
   */
  private calculateMatchConfidence(original: string, matched: string): number {
    if (original === matched) return 1.0;
    if (this.normalizeReference(original) === this.normalizeReference(matched))
      return 0.9;
    
    // Check for common transformations
    if (original.toLowerCase() === matched.toLowerCase()) return 0.8;
    
    // Check if it's just a prefix/suffix difference
    const origNorm = this.normalizeReference(original);
    const matchNorm = this.normalizeReference(matched);
    if (origNorm.includes(matchNorm) || matchNorm.includes(origNorm)) {
      return 0.7;
    }
    
    return 0.6; // Fuzzy match
  }

  /**
   * Validate a reference format
   */
  validateReferenceFormat(reference: string): {
    isValid: boolean;
    format: string;
    suggestions: string[];
  } {
    const suggestions: string[] = [];
    let format = 'unknown';
    let isValid = true;

    // Common patterns
    if (/^\d+$/.test(reference)) {
      format = 'numeric';
    } else if (/^\d+[a-z]$/.test(reference)) {
      format = 'numeric-alpha';
    } else if (/^\d+\([a-z]\)$/.test(reference)) {
      format = 'numeric-parenthetical';
    } else if (/^\d+\.\d+$/.test(reference)) {
      format = 'decimal';
    } else if (/^[ivx]+$/i.test(reference)) {
      format = 'roman';
      suggestions.push(this.convertRomanToArabic(reference.toLowerCase()));
    } else if (/^(clause|section|paragraph|article)\s+\d+/i.test(reference)) {
      format = 'prefixed';
      suggestions.push(reference.replace(/^(clause|section|paragraph|article)\s+/i, ''));
    } else {
      isValid = false;
      format = 'invalid';
      suggestions.push('Use format like: 4, 4a, 4(a), 4.1, or Section 4');
    }

    return { isValid, format, suggestions };
  }

  /**
   * Find potential reference matches in text
   */
  findReferencesInText(text: string): Array<{
    match: string;
    position: number;
    confidence: number;
    type: string;
  }> {
    const references: Array<{
      match: string;
      position: number;
      confidence: number;
      type: string;
    }> = [];

    // Patterns for different reference types
    const patterns = [
      { regex: /\b(clause|section|paragraph|article)\s+(\d+(?:[a-z]|\([a-z]\)|\.\d+)?)/gi, type: 'prefixed', confidence: 0.9 },
      { regex: /\b(\d+\([a-z]\))/g, type: 'parenthetical', confidence: 0.8 },
      { regex: /\b(\d+[a-z])\b/g, type: 'alpha-numeric', confidence: 0.7 },
      { regex: /\b(\d+\.\d+)\b/g, type: 'decimal', confidence: 0.8 },
      { regex: /\b([ivx]+)\b/gi, type: 'roman', confidence: 0.6 }
    ];

    patterns.forEach(({ regex, type, confidence }) => {
      let match;
      while ((match = regex.exec(text)) !== null) {
        references.push({
          match: match[0],
          position: match.index,
          confidence,
          type
        });
      }
    });

    // Sort by position
    return references.sort((a, b) => a.position - b.position);
  }

  /**
   * Get statistics about reference resolution
   */
  getResolutionStats(obligations: Obligation[]): {
    totalReferences: number;
    resolvedCount: number;
    unresolvedCount: number;
    resolutionRate: number;
    commonIssues: string[];
  } {
    let totalReferences = 0;
    let resolvedCount = 0;
    let unresolvedCount = 0;
    const issues: string[] = [];

    obligations.forEach(obl => {
      totalReferences += obl.references.length;
      resolvedCount += obl.resolvedReferences?.length || 0;
      unresolvedCount += obl.unresolvedReferences?.length || 0;
      
      obl.unresolvedReferences?.forEach(unresolved => {
        issues.push(unresolved.reason);
      });
    });

    const resolutionRate = totalReferences > 0 ? resolvedCount / totalReferences : 0;
    
    // Count common issues
    const issueCounts = issues.reduce((acc, issue) => {
      acc[issue] = (acc[issue] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const commonIssues = Object.entries(issueCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([issue]) => issue);

    return {
      totalReferences,
      resolvedCount,
      unresolvedCount,
      resolutionRate,
      commonIssues
    };
  }
}
