import { 
  ClauseAnalysisResult, 
  ReviewItem, 
  ReviewReason, 
  Obligation 
} from '../types/index.js';

/**
 * Service for flagging clauses and obligations that need human review
 */
export class ReviewFlagService {
  
  /**
   * Flag clauses that need human review
   */
  flagForReview(
    result: ClauseAnalysisResult,
    reason: ReviewReason
  ): ReviewItem {
    return {
      id: `REVIEW-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      clauseNumber: result.clauseNumber,
      clauseText: result.clauseText,
      reason: reason.type,
      details: reason.details,
      priority: this.calculatePriority(reason),
      timestamp: new Date(),
      obligations: result.obligations.filter((o) => o.needsReview),
      suggestedActions: this.getSuggestedActions(reason),
    };
  }

  /**
   * Analyze extraction results and automatically flag issues
   */
  analyzeAndFlag(results: ClauseAnalysisResult[]): ReviewItem[] {
    const reviewItems: ReviewItem[] = [];

    results.forEach(result => {
      const issues = this.identifyIssues(result);
      
      issues.forEach(issue => {
        const reviewItem = this.flagForReview(result, issue);
        reviewItems.push(reviewItem);
      });
    });

    return reviewItems.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Identify issues in extraction results
   */
  private identifyIssues(result: ClauseAnalysisResult): ReviewReason[] {
    const issues: ReviewReason[] = [];

    // Check for unresolved references
    const unresolvedRefs = result.obligations.filter(o => 
      o.unresolvedReferences && o.unresolvedReferences.length > 0
    );
    if (unresolvedRefs.length > 0) {
      issues.push({
        type: 'unresolved-references',
        details: {
          count: unresolvedRefs.length,
          references: unresolvedRefs.flatMap(o => o.unresolvedReferences || [])
        }
      });
    }

    // Check for low confidence
    if (result.confidence && result.confidence < 0.6) {
      issues.push({
        type: 'low-confidence',
        details: {
          confidence: result.confidence,
          threshold: 0.6
        }
      });
    }

    // Check for complex conditions
    const complexObligations = result.obligations.filter(o => 
      o.conditions.length > 3 || 
      o.conditions.some(c => c.logic === 'NOT') ||
      o.conditions.some(c => c.text.length > 200)
    );
    if (complexObligations.length > 0) {
      issues.push({
        type: 'complex-conditions',
        details: {
          count: complexObligations.length,
          obligations: complexObligations.map(o => ({
            id: o.id,
            conditionCount: o.conditions.length,
            hasNegation: o.conditions.some(c => c.logic === 'NOT')
          }))
        }
      });
    }

    // Check for ambiguous language
    const ambiguousObligations = result.obligations.filter(o => 
      this.hasAmbiguousLanguage(o.text)
    );
    if (ambiguousObligations.length > 0) {
      issues.push({
        type: 'ambiguous-language',
        details: {
          count: ambiguousObligations.length,
          obligations: ambiguousObligations.map(o => ({
            id: o.id,
            text: o.text,
            ambiguousTerms: this.findAmbiguousTerms(o.text)
          }))
        }
      });
    }

    // Check for extraction failures
    if (result.skippedReason && result.skippedReason.includes('failed')) {
      issues.push({
        type: 'extraction-failed',
        details: {
          reason: result.skippedReason,
          clauseLength: result.clauseText.length
        }
      });
    }

    return issues;
  }

  /**
   * Calculate priority score for review items
   */
  private calculatePriority(reason: ReviewReason): number {
    const priorityMap: Record<string, number> = {
      'extraction-failed': 10,
      'unresolved-references': 8,
      'complex-conditions': 7,
      'ambiguous-language': 6,
      'low-confidence': 5,
    };

    let basePriority = priorityMap[reason.type] || 5;

    // Adjust priority based on details
    switch (reason.type) {
      case 'unresolved-references':
        if (reason.details.count > 3) basePriority += 1;
        break;
      case 'low-confidence':
        if (reason.details.confidence < 0.4) basePriority += 2;
        else if (reason.details.confidence < 0.5) basePriority += 1;
        break;
      case 'complex-conditions':
        if (reason.details.count > 5) basePriority += 1;
        break;
      case 'ambiguous-language':
        if (reason.details.count > 2) basePriority += 1;
        break;
    }

    return Math.min(basePriority, 10);
  }

  /**
   * Get suggested actions for different review reasons
   */
  private getSuggestedActions(reason: ReviewReason): string[] {
    switch (reason.type) {
      case 'unresolved-references':
        return [
          'Verify if referenced clauses exist in the document',
          'Check for typos in clause references',
          'Confirm the clause numbering system used',
          'Consider if references point to external documents',
        ];
      case 'low-confidence':
        return [
          'Review keyword matching results',
          'Check if clause actually contains obligations',
          'Verify LLM extraction accuracy',
          'Consider manual obligation identification',
        ];
      case 'ambiguous-language':
        return [
          'Clarify party responsibilities',
          'Define ambiguous terms and phrases',
          'Specify concrete actions required',
          'Review obligation scope and limitations',
        ];
      case 'complex-conditions':
        return [
          'Break down complex conditions into simpler parts',
          'Verify condition logic (AND/OR/NOT)',
          'Check for nested conditional statements',
          'Confirm condition precedence and grouping',
        ];
      case 'extraction-failed':
        return [
          'Check clause text for formatting issues',
          'Verify API connectivity and rate limits',
          'Review error logs for specific failure reasons',
          'Consider manual extraction as fallback',
        ];
      default:
        return ['Manual review required'];
    }
  }

  /**
   * Check if text contains ambiguous language
   */
  private hasAmbiguousLanguage(text: string): boolean {
    const ambiguousTerms = [
      'reasonable', 'appropriate', 'adequate', 'sufficient',
      'promptly', 'timely', 'as soon as possible', 'best efforts',
      'material', 'substantial', 'significant', 'minor',
      'may', 'might', 'could', 'should consider',
      'to the extent', 'where applicable', 'if necessary'
    ];

    const lowerText = text.toLowerCase();
    return ambiguousTerms.some(term => lowerText.includes(term));
  }

  /**
   * Find specific ambiguous terms in text
   */
  private findAmbiguousTerms(text: string): string[] {
    const ambiguousTerms = [
      'reasonable', 'appropriate', 'adequate', 'sufficient',
      'promptly', 'timely', 'as soon as possible', 'best efforts',
      'material', 'substantial', 'significant', 'minor',
      'may', 'might', 'could', 'should consider',
      'to the extent', 'where applicable', 'if necessary'
    ];

    const lowerText = text.toLowerCase();
    return ambiguousTerms.filter(term => lowerText.includes(term));
  }

  /**
   * Generate review summary report
   */
  generateReviewSummary(reviewItems: ReviewItem[]): {
    totalItems: number;
    byPriority: Record<string, number>;
    byReason: Record<string, number>;
    averagePriority: number;
    urgentItems: ReviewItem[];
  } {
    const totalItems = reviewItems.length;
    
    const byPriority = reviewItems.reduce((acc, item) => {
      const priority = item.priority >= 8 ? 'high' : item.priority >= 6 ? 'medium' : 'low';
      acc[priority] = (acc[priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byReason = reviewItems.reduce((acc, item) => {
      acc[item.reason] = (acc[item.reason] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const averagePriority = totalItems > 0 
      ? reviewItems.reduce((sum, item) => sum + item.priority, 0) / totalItems 
      : 0;

    const urgentItems = reviewItems.filter(item => item.priority >= 8);

    return {
      totalItems,
      byPriority,
      byReason,
      averagePriority,
      urgentItems
    };
  }

  /**
   * Filter review items by criteria
   */
  filterReviewItems(
    items: ReviewItem[],
    filters: {
      minPriority?: number;
      maxPriority?: number;
      reasons?: string[];
      clauseNumbers?: string[];
      hasObligations?: boolean;
    }
  ): ReviewItem[] {
    return items.filter(item => {
      if (filters.minPriority && item.priority < filters.minPriority) return false;
      if (filters.maxPriority && item.priority > filters.maxPriority) return false;
      if (filters.reasons && !filters.reasons.includes(item.reason)) return false;
      if (filters.clauseNumbers && item.clauseNumber && !filters.clauseNumbers.includes(item.clauseNumber)) return false;
      if (filters.hasObligations !== undefined && (item.obligations.length > 0) !== filters.hasObligations) return false;
      
      return true;
    });
  }

  /**
   * Mark review item as resolved
   */
  resolveReviewItem(item: ReviewItem, resolution: string, reviewer: string): ReviewItem & {
    resolved: true;
    resolution: string;
    reviewer: string;
    resolvedAt: Date;
  } {
    return {
      ...item,
      resolved: true,
      resolution,
      reviewer,
      resolvedAt: new Date()
    };
  }
}
