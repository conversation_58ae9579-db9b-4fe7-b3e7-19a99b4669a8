import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * Service for managing LLM prompts stored as .md files with placeholder substitution
 */
export class PromptManagerService {
  private promptsDir: string;
  private promptCache = new Map<string, string>();

  constructor(promptsDir?: string) {
    this.promptsDir = promptsDir || path.join(process.cwd(), 'prompts');
  }

  /**
   * Load prompt from .md file and replace placeholders
   */
  async loadPrompt(
    promptPath: string,
    variables: Record<string, any>
  ): Promise<string> {
    const fullPath = path.join(this.promptsDir, promptPath);

    // Check cache first
    let template = this.promptCache.get(fullPath);
    if (!template) {
      try {
        template = await fs.readFile(fullPath, 'utf-8');
        this.promptCache.set(fullPath, template);
      } catch (error) {
        throw new Error(`Failed to load prompt from ${fullPath}: ${error}`);
      }
    }

    // Replace placeholders
    return this.replacePlaceholders(template, variables);
  }

  /**
   * Replace placeholders in template with variables
   * Supports {{VARIABLE_NAME}} syntax
   */
  private replacePlaceholders(
    template: string,
    variables: Record<string, any>
  ): string {
    let result = template;

    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`;
      const stringValue = this.formatValue(value);
      result = result.replace(new RegExp(placeholder, 'g'), stringValue);
    }

    // Check for unreplaced placeholders
    const unreplacedPlaceholders = result.match(/\{\{[^}]+\}\}/g);
    if (unreplacedPlaceholders) {
      console.warn('Unreplaced placeholders found:', unreplacedPlaceholders);
    }

    return result;
  }

  /**
   * Format values for insertion into prompts
   */
  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return 'N/A';
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    
    return String(value);
  }

  /**
   * Get system context for different prompt types
   */
  getSystemContext(type: 'extraction' | 'validation' | 'analysis'): string {
    const contexts = {
      extraction:
        'You are a legal contract analyzer specializing in identifying obligations and duties in contract clauses. ' +
        'You extract precise obligations with accurate text anchoring for regex matching. ' +
        'Always return valid JSON and be thorough in identifying all obligations.',
      
      validation:
        'You are an expert at validating legal obligation extractions for completeness and accuracy. ' +
        'You check that all obligations are properly identified and that text anchoring is precise. ' +
        'You flag any issues or missing obligations.',
      
      analysis:
        'You are an expert at analyzing the detailed components of legal obligations. ' +
        'You identify conditions, events, temporal expressions, and references within obligations. ' +
        'You provide structured analysis that helps understand obligation complexity.'
    };

    return contexts[type];
  }

  /**
   * Load and validate a prompt template
   */
  async validatePrompt(promptPath: string): Promise<{
    isValid: boolean;
    placeholders: string[];
    errors: string[];
  }> {
    try {
      const fullPath = path.join(this.promptsDir, promptPath);
      const template = await fs.readFile(fullPath, 'utf-8');
      
      // Extract placeholders
      const placeholderMatches = template.match(/\{\{[^}]+\}\}/g) || [];
      const placeholders = placeholderMatches.map(match => 
        match.replace(/[{}]/g, '')
      );

      const errors: string[] = [];

      // Check for common issues
      if (template.length < 50) {
        errors.push('Prompt template seems too short');
      }

      if (!template.includes('{{')) {
        errors.push('No placeholders found in template');
      }

      // Check for malformed placeholders
      const malformedPlaceholders = template.match(/\{[^{]|[^}]\}/g);
      if (malformedPlaceholders) {
        errors.push(`Malformed placeholders found: ${malformedPlaceholders.join(', ')}`);
      }

      return {
        isValid: errors.length === 0,
        placeholders: [...new Set(placeholders)], // Remove duplicates
        errors
      };
    } catch (error) {
      return {
        isValid: false,
        placeholders: [],
        errors: [`Failed to read prompt file: ${error}`]
      };
    }
  }

  /**
   * Clear the prompt cache
   */
  clearCache(): void {
    this.promptCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.promptCache.size,
      keys: Array.from(this.promptCache.keys())
    };
  }

  /**
   * Preload commonly used prompts
   */
  async preloadPrompts(promptPaths: string[]): Promise<void> {
    const loadPromises = promptPaths.map(async (promptPath) => {
      try {
        const fullPath = path.join(this.promptsDir, promptPath);
        const template = await fs.readFile(fullPath, 'utf-8');
        this.promptCache.set(fullPath, template);
      } catch (error) {
        console.warn(`Failed to preload prompt ${promptPath}:`, error);
      }
    });

    await Promise.all(loadPromises);
  }

  /**
   * List available prompt files
   */
  async listPrompts(): Promise<string[]> {
    try {
      const files = await this.getPromptFiles(this.promptsDir);
      return files.map(file => path.relative(this.promptsDir, file));
    } catch (error) {
      console.error('Failed to list prompts:', error);
      return [];
    }
  }

  /**
   * Recursively get all .md files in prompts directory
   */
  private async getPromptFiles(dir: string): Promise<string[]> {
    const files: string[] = [];
    
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          const subFiles = await this.getPromptFiles(fullPath);
          files.push(...subFiles);
        } else if (entry.isFile() && entry.name.endsWith('.md')) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Directory might not exist yet
    }
    
    return files;
  }

  /**
   * Create a new prompt template
   */
  async createPrompt(promptPath: string, content: string): Promise<void> {
    const fullPath = path.join(this.promptsDir, promptPath);
    const dir = path.dirname(fullPath);
    
    // Ensure directory exists
    await fs.mkdir(dir, { recursive: true });
    
    // Write the prompt file
    await fs.writeFile(fullPath, content, 'utf-8');
    
    // Clear cache for this file if it exists
    this.promptCache.delete(fullPath);
  }
}
