/**
 * Core types for clause-level obligation and duty extraction system
 */

export interface ClauseAnalysisResult {
  clauseNumber?: string;
  clauseText: string;
  containsObligations: boolean;
  obligations: Obligation[];
  keywordsFound?: string[];
  confidence?: number;
  skippedReason?: string;
}

export interface Obligation {
  id: string;
  clauseNumber?: string;
  text: string;
  type: 'duty' | 'obligation' | 'requirement';
  party: string;
  action: string;

  // Text anchoring for regex extraction
  startingQuote: string; // First 1-8 words of obligation text
  endingQuote: string;   // Last 1-8 words of obligation text

  // Sub-elements
  conditions: Condition[];
  events: Event[];
  temporalExpressions: TemporalExpression[];
  references: Reference[];

  // Resolution tracking
  resolvedReferences?: ResolvedReference[];
  unresolvedReferences?: UnresolvedReference[];

  // Status
  validationStatus: 'pending' | 'validated' | 'flagged';
  needsReview: boolean;
}

export interface Condition {
  type: 'prerequisite' | 'exception' | 'qualifier';
  text: string;
  logic: 'AND' | 'OR' | 'NOT';
}

export interface Event {
  type: 'trigger' | 'termination' | 'milestone';
  text: string;
  timing?: 'before' | 'after' | 'during';
}

export interface TemporalExpression {
  text: string;
  type: 'deadline' | 'duration' | 'frequency' | 'start_date' | 'end_date';
  parsedDate?: Date;
  parsedDuration?: number; // in days
}

export interface Reference {
  target: string;
  type: 'clause_reference' | 'document_reference' | 'external_reference';
  context?: string;
}

export interface UnresolvedReference extends Reference {
  reason: string;
  attemptedVariations: string[];
}

export interface ResolvedReference extends Reference {
  resolvedTo: string;
  confidence: number;
}

export interface ReviewItem {
  id: string;
  clauseNumber?: string;
  clauseText: string;
  reason: string;
  details: any;
  priority: number;
  timestamp: Date;
  obligations: Obligation[];
  suggestedActions: string[];
}

export interface ReviewReason {
  type: 'unresolved-references' | 'low-confidence' | 'ambiguous-language' | 'complex-conditions' | 'extraction-failed';
  details: any;
}

export interface ClauseSearchResult {
  containsObligations: boolean;
  keywords: string[];
  fullClause: string;
  confidence?: number;
}

// Text anchoring types
export interface ExtractedQuote {
  text: string;
  startIndex: number;
  endIndex: number;
  confidence: number;
}

export interface TextHighlight {
  obligationId: string;
  startIndex: number;
  endIndex: number;
  text: string;
  cssClass: string;
  confidence: number;
}

export interface HighlightedClause {
  originalText: string;
  highlights: TextHighlight[];
  highlightedHtml: string;
}

// Queue management types
export interface QueueStats {
  running: number;
  queued: number;
  done: number;
}

// Configuration types
export interface ExtractionConfig {
  geminiApiKey: string;
  requestsPerMinute?: number;
  maxConcurrent?: number;
  minTimeBetweenRequests?: number;
  retryDelay?: number;
  maxRetryTime?: number;
}
