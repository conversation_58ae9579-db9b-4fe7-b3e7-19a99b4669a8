#!/usr/bin/env node

/**
 * Batch processing example for the Obligation Extraction System
 * 
 * This example demonstrates how to:
 * 1. Process multiple clauses in batch
 * 2. Handle rate limiting and queue management
 * 3. Generate comprehensive reports
 * 4. Export results to different formats
 */

import { ObligationExtractorService } from '../src/services/obligation-extractor.service.js';
import { KeywordSearchService } from '../src/services/keyword-search.service.js';
import { QueueManagerService } from '../src/services/queue-manager.service.js';
import { PromptManagerService } from '../src/services/prompt-manager.service.js';
import { ReferenceResolverService } from '../src/services/reference-resolver.service.js';
import { TextAnchorService } from '../src/services/text-anchor.service.js';
import { ReviewFlagService } from '../src/services/review-flag.service.js';
import { getConfig, validateConfig } from './config.example.js';
import { ClauseAnalysisResult } from '../src/types/index.js';

// Sample contract clauses for batch processing
const sampleClauses = [
  {
    number: '2.1',
    text: `Payment Terms. The Client shall pay the Contractor the total amount of $50,000 
           in three equal installments: (a) $16,667 upon execution of this Agreement, 
           (b) $16,667 upon delivery of the first milestone as defined in Schedule A, 
           and (c) $16,666 upon final completion and acceptance of all deliverables.`
  },
  {
    number: '3.2',
    text: `Delivery Schedule. The Contractor must deliver all software components according 
           to the timeline specified in Exhibit B. Each delivery shall be accompanied by 
           complete documentation and source code. The Client shall have 10 business days 
           to review and provide feedback on each delivery.`
  },
  {
    number: '4.1',
    text: `Warranty. The Contractor warrants that the software will be free from material 
           defects for a period of 12 months from the date of final acceptance. If any 
           defects are discovered during this period, the Contractor shall remedy such 
           defects at no additional cost within 30 days of written notice.`
  },
  {
    number: '5.3',
    text: `Confidentiality. Both parties agree to maintain the confidentiality of all 
           proprietary information disclosed during the performance of this Agreement. 
           This obligation shall survive termination of this Agreement for a period of 
           5 years.`
  },
  {
    number: '6.1',
    text: `Intellectual Property. All intellectual property rights in the software 
           developed under this Agreement shall vest in the Client upon full payment. 
           The Contractor retains rights to any pre-existing intellectual property 
           and general methodologies used in the development.`
  },
  {
    number: '7.2',
    text: `Termination. Either party may terminate this Agreement upon 30 days written 
           notice. In the event of termination, the Contractor shall deliver all work 
           product completed as of the termination date, and the Client shall pay for 
           all work satisfactorily completed.`
  }
];

async function main() {
  console.log('🚀 Starting Batch Processing Example\n');

  // Initialize system
  const config = getConfig();
  const configErrors = validateConfig(config);
  
  if (configErrors.length > 0) {
    console.error('❌ Configuration errors:');
    configErrors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }

  // Initialize services
  const keywordSearch = new KeywordSearchService();
  const queueManager = new QueueManagerService(config);
  const promptManager = new PromptManagerService();
  const referenceResolver = new ReferenceResolverService();
  const textAnchor = new TextAnchorService();
  const reviewFlag = new ReviewFlagService();

  const extractor = new ObligationExtractorService(
    keywordSearch,
    queueManager,
    promptManager,
    referenceResolver,
    textAnchor
  );

  console.log(`📄 Processing ${sampleClauses.length} clauses...\n`);

  try {
    // Process all clauses
    const startTime = Date.now();
    const results: ClauseAnalysisResult[] = [];

    for (let i = 0; i < sampleClauses.length; i++) {
      const clause = sampleClauses[i];
      console.log(`Processing clause ${clause.number} (${i + 1}/${sampleClauses.length})...`);
      
      const result = await extractor.extractObligationsFromClause(clause.text, clause.number);
      results.push(result);
      
      // Show progress
      const queueStats = queueManager.getQueueStats();
      console.log(`  Found ${result.obligations.length} obligations (Queue: ${queueStats.running} running, ${queueStats.queued} queued)`);
    }

    const processingTime = Date.now() - startTime;
    console.log(`\n✅ Batch processing completed in ${processingTime}ms\n`);

    // Generate comprehensive statistics
    console.log('📊 Batch Processing Results:');
    const stats = extractor.getExtractionStats(results);
    console.log(`  Total clauses processed: ${stats.totalClauses}`);
    console.log(`  Clauses with obligations: ${stats.clausesWithObligations}`);
    console.log(`  Total obligations extracted: ${stats.totalObligations}`);
    console.log(`  Average obligations per clause: ${stats.averageObligationsPerClause.toFixed(1)}`);
    console.log(`  Average confidence: ${stats.averageConfidence.toFixed(2)}`);
    console.log(`  Clauses needing review: ${stats.clausesNeedingReview}`);

    // Analyze review items
    console.log('\n🔍 Review Analysis:');
    const reviewItems = reviewFlag.analyzeAndFlag(results);
    const reviewSummary = reviewFlag.generateReviewSummary(reviewItems);
    
    console.log(`  Total review items: ${reviewSummary.totalItems}`);
    console.log(`  High priority: ${reviewSummary.byPriority.high || 0}`);
    console.log(`  Medium priority: ${reviewSummary.byPriority.medium || 0}`);
    console.log(`  Low priority: ${reviewSummary.byPriority.low || 0}`);
    console.log(`  Average priority: ${reviewSummary.averagePriority.toFixed(1)}`);

    if (reviewSummary.urgentItems.length > 0) {
      console.log('\n⚠️  Urgent Review Items:');
      reviewSummary.urgentItems.forEach((item, index) => {
        console.log(`  ${index + 1}. Clause ${item.clauseNumber}: ${item.reason} (Priority: ${item.priority})`);
      });
    }

    // Generate detailed clause-by-clause report
    console.log('\n📋 Detailed Results by Clause:');
    results.forEach((result, index) => {
      console.log(`\n  Clause ${result.clauseNumber}:`);
      console.log(`    Obligations: ${result.obligations.length}`);
      console.log(`    Confidence: ${result.confidence?.toFixed(2) || 'N/A'}`);
      
      if (result.obligations.length > 0) {
        result.obligations.forEach((obl, oblIndex) => {
          console.log(`      ${oblIndex + 1}. ${obl.party} must ${obl.action}`);
          if (obl.needsReview) console.log(`         ⚠️  Needs review`);
        });
      } else if (result.skippedReason) {
        console.log(`    Skipped: ${result.skippedReason}`);
      }
    });

    // Export results to JSON
    console.log('\n💾 Exporting results...');
    const fs = await import('fs');
    
    const exportData = {
      metadata: {
        processedAt: new Date().toISOString(),
        processingTimeMs: processingTime,
        configuration: {
          requestsPerMinute: config.requestsPerMinute,
          maxConcurrent: config.maxConcurrent,
          minConfidenceThreshold: config.minConfidenceThreshold
        }
      },
      statistics: stats,
      reviewSummary,
      results: results.map(r => ({
        clauseNumber: r.clauseNumber,
        containsObligations: r.containsObligations,
        confidence: r.confidence,
        obligationCount: r.obligations.length,
        obligations: r.obligations.map(o => ({
          id: o.id,
          type: o.type,
          party: o.party,
          action: o.action,
          text: o.text,
          needsReview: o.needsReview,
          conditionsCount: o.conditions.length,
          eventsCount: o.events.length,
          temporalCount: o.temporalExpressions.length,
          referencesCount: o.references.length
        })),
        keywordsFound: r.keywordsFound,
        skippedReason: r.skippedReason
      })),
      reviewItems: reviewItems.map(item => ({
        clauseNumber: item.clauseNumber,
        reason: item.reason,
        priority: item.priority,
        obligationCount: item.obligations.length,
        suggestedActions: item.suggestedActions
      }))
    };

    fs.writeFileSync('batch-results.json', JSON.stringify(exportData, null, 2));
    console.log('  📄 Results exported to batch-results.json');

    // Generate CSV summary
    const csvLines = [
      'Clause,Obligations,Confidence,Keywords,Needs Review,Skip Reason'
    ];
    
    results.forEach(result => {
      const needsReview = result.obligations.some(o => o.needsReview) ? 'Yes' : 'No';
      const keywords = result.keywordsFound?.join(';') || '';
      csvLines.push([
        result.clauseNumber || '',
        result.obligations.length.toString(),
        result.confidence?.toFixed(2) || '',
        keywords,
        needsReview,
        result.skippedReason || ''
      ].join(','));
    });

    fs.writeFileSync('batch-summary.csv', csvLines.join('\n'));
    console.log('  📊 Summary exported to batch-summary.csv');

    // Performance analysis
    console.log('\n⚡ Performance Analysis:');
    const queueStats = queueManager.getDetailedStats();
    console.log(`  Total API calls: ${queueStats.counts.DONE}`);
    console.log(`  Average time per clause: ${(processingTime / results.length).toFixed(0)}ms`);
    console.log(`  Queue efficiency: ${queueStats.settings.maxConcurrent} max concurrent`);

  } catch (error) {
    console.error('\n❌ Error during batch processing:', error);
    process.exit(1);
  } finally {
    await queueManager.shutdown();
  }

  console.log('\n🎉 Batch processing completed successfully!');
  console.log('\nGenerated files:');
  console.log('  - batch-results.json: Complete extraction results');
  console.log('  - batch-summary.csv: Summary statistics');
  console.log('\nNext steps:');
  console.log('  - Review the generated files');
  console.log('  - Address any high-priority review items');
  console.log('  - Adjust configuration for your specific use case');
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
