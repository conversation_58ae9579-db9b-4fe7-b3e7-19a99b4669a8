#!/usr/bin/env node

/**
 * System test script for the Obligation Extraction System
 * 
 * This script performs basic integration tests to verify:
 * 1. All services can be instantiated
 * 2. Configuration is valid
 * 3. Prompts can be loaded
 * 4. Keyword search works
 * 5. Text anchoring works
 * 6. Reference resolution works
 * 7. Review flagging works
 */

import { KeywordSearchService } from '../src/services/keyword-search.service.js';
import { QueueManagerService } from '../src/services/queue-manager.service.js';
import { PromptManagerService } from '../src/services/prompt-manager.service.js';
import { ReferenceResolverService } from '../src/services/reference-resolver.service.js';
import { TextAnchorService } from '../src/services/text-anchor.service.js';
import { ReviewFlagService } from '../src/services/review-flag.service.js';
import { ObligationExtractorService } from '../src/services/obligation-extractor.service.js';
import { getConfig, validateConfig } from './config.example.js';

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  details?: any;
}

class SystemTester {
  private results: TestResult[] = [];

  async runTest(name: string, testFn: () => Promise<any>): Promise<void> {
    console.log(`🧪 Testing: ${name}`);
    
    try {
      const result = await testFn();
      this.results.push({
        name,
        passed: true,
        details: result
      });
      console.log(`  ✅ PASSED`);
    } catch (error) {
      this.results.push({
        name,
        passed: false,
        error: error instanceof Error ? error.message : String(error)
      });
      console.log(`  ❌ FAILED: ${error}`);
    }
  }

  getResults(): { passed: number; failed: number; total: number; results: TestResult[] } {
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    
    return {
      passed,
      failed,
      total: this.results.length,
      results: this.results
    };
  }
}

async function main() {
  console.log('🚀 Starting System Integration Tests\n');
  
  const tester = new SystemTester();

  // Test 1: Configuration validation
  await tester.runTest('Configuration Validation', async () => {
    const config = getConfig();
    const errors = validateConfig(config);
    
    if (errors.length > 0) {
      throw new Error(`Configuration errors: ${errors.join(', ')}`);
    }
    
    return { configValid: true, environment: process.env.NODE_ENV || 'development' };
  });

  // Test 2: Service instantiation
  await tester.runTest('Service Instantiation', async () => {
    const config = getConfig();
    
    const keywordSearch = new KeywordSearchService();
    const queueManager = new QueueManagerService(config);
    const promptManager = new PromptManagerService();
    const referenceResolver = new ReferenceResolverService();
    const textAnchor = new TextAnchorService();
    const reviewFlag = new ReviewFlagService();
    
    const extractor = new ObligationExtractorService(
      keywordSearch,
      queueManager,
      promptManager,
      referenceResolver,
      textAnchor
    );

    return { servicesCreated: 7 };
  });

  // Test 3: Prompt loading
  await tester.runTest('Prompt Loading', async () => {
    const promptManager = new PromptManagerService();
    
    const extractionPrompt = await promptManager.loadPrompt('extraction/find-obligations');
    const conditionsPrompt = await promptManager.loadPrompt('analysis/extract-conditions');
    const validationPrompt = await promptManager.loadPrompt('validation/validate-extraction');
    
    if (!extractionPrompt.includes('{{CLAUSE_TEXT}}')) {
      throw new Error('Extraction prompt missing required placeholder');
    }
    
    if (!conditionsPrompt.includes('{{OBLIGATION_TEXT}}')) {
      throw new Error('Conditions prompt missing required placeholder');
    }
    
    return { 
      promptsLoaded: 3,
      extractionLength: extractionPrompt.length,
      conditionsLength: conditionsPrompt.length,
      validationLength: validationPrompt.length
    };
  });

  // Test 4: Keyword search functionality
  await tester.runTest('Keyword Search', async () => {
    const keywordSearch = new KeywordSearchService();
    
    const testClause = "The Contractor shall deliver the software within 30 days and must provide ongoing support.";
    const result = keywordSearch.searchForObligationKeywords(testClause);
    
    if (!result.hasObligationKeywords) {
      throw new Error('Failed to detect obligation keywords in test clause');
    }
    
    if (result.confidence <= 0) {
      throw new Error('Confidence score should be greater than 0');
    }
    
    return {
      hasKeywords: result.hasObligationKeywords,
      confidence: result.confidence,
      keywordsFound: result.keywordsFound,
      patterns: result.patterns
    };
  });

  // Test 5: Text anchoring
  await tester.runTest('Text Anchoring', async () => {
    const textAnchor = new TextAnchorService();
    
    const clauseText = "The Contractor shall deliver the completed software by December 31, 2024.";
    const obligationText = "The Contractor shall deliver the completed software by December 31, 2024";
    
    const anchors = textAnchor.generateOptimalAnchors(clauseText, obligationText);
    
    if (!anchors.startingQuote || !anchors.endingQuote) {
      throw new Error('Failed to generate text anchors');
    }
    
    const extracted = textAnchor.extractTextUsingAnchors(
      clauseText,
      anchors.startingQuote,
      anchors.endingQuote
    );
    
    if (!extracted.success) {
      throw new Error('Failed to extract text using generated anchors');
    }
    
    return {
      startingQuote: anchors.startingQuote,
      endingQuote: anchors.endingQuote,
      extractedText: extracted.extractedText,
      confidence: anchors.confidence
    };
  });

  // Test 6: Reference resolution
  await tester.runTest('Reference Resolution', async () => {
    const referenceResolver = new ReferenceResolverService();
    
    const testReferences = [
      'Section 4.2',
      'Clause 3(a)',
      'Article IV',
      'paragraph 2.1.3'
    ];
    
    const results = testReferences.map(ref => {
      const normalized = referenceResolver.normalizeReference(ref);
      return {
        original: ref,
        normalized: normalized.normalizedForm,
        confidence: normalized.confidence
      };
    });
    
    if (results.some(r => r.confidence <= 0)) {
      throw new Error('Some references failed to normalize');
    }
    
    return { referencesProcessed: results.length, results };
  });

  // Test 7: Review flagging
  await tester.runTest('Review Flagging', async () => {
    const reviewFlag = new ReviewFlagService();
    
    // Create mock analysis results
    const mockResults = [{
      clauseNumber: '4.2',
      containsObligations: true,
      confidence: 0.5, // Low confidence should trigger review
      obligations: [{
        id: 'test-1',
        type: 'performance' as const,
        party: 'Unknown Party', // Unknown party should trigger review
        action: 'do something',
        text: 'test obligation',
        startingQuote: 'test',
        endingQuote: 'obligation',
        conditions: [],
        events: [],
        temporalExpressions: [],
        references: [{ 
          text: 'Section 999', 
          target: 'Section 999', 
          type: 'internal_clause' as const,
          relationship: 'depends_on',
          context: 'test'
        }], // Unresolved reference should trigger review
        needsReview: false
      }],
      clauseText: 'test clause',
      keywordsFound: ['shall'],
      processingTime: 1000
    }];
    
    const reviewItems = reviewFlag.analyzeAndFlag(mockResults);
    
    if (reviewItems.length === 0) {
      throw new Error('Expected review items to be flagged');
    }
    
    const summary = reviewFlag.generateReviewSummary(reviewItems);
    
    return {
      reviewItemsFound: reviewItems.length,
      totalItems: summary.totalItems,
      averagePriority: summary.averagePriority,
      urgentItems: summary.urgentItems.length
    };
  });

  // Test 8: End-to-end integration (without API call)
  await tester.runTest('End-to-End Integration (Mock)', async () => {
    const config = { ...getConfig(), geminiApiKey: 'test-key' };
    
    const keywordSearch = new KeywordSearchService();
    const queueManager = new QueueManagerService(config);
    const promptManager = new PromptManagerService();
    const referenceResolver = new ReferenceResolverService();
    const textAnchor = new TextAnchorService();
    
    const extractor = new ObligationExtractorService(
      keywordSearch,
      queueManager,
      promptManager,
      referenceResolver,
      textAnchor
    );
    
    const testClause = "The Contractor shall deliver software within 30 days.";
    
    // Test keyword search phase
    const keywordResult = keywordSearch.searchForObligationKeywords(testClause);
    
    if (!keywordResult.hasObligationKeywords) {
      throw new Error('Keyword search failed');
    }
    
    // Test prompt generation
    const prompt = await promptManager.loadPrompt('extraction/find-obligations');
    const filledPrompt = promptManager.fillPromptTemplate(prompt, {
      CLAUSE_TEXT: testClause
    });
    
    if (!filledPrompt.includes(testClause)) {
      throw new Error('Prompt template filling failed');
    }
    
    await queueManager.shutdown();
    
    return {
      keywordSearchPassed: true,
      promptGenerationPassed: true,
      confidence: keywordResult.confidence,
      keywordsFound: keywordResult.keywordsFound
    };
  });

  // Display results
  console.log('\n📊 Test Results Summary:');
  const summary = tester.getResults();
  
  console.log(`  Total tests: ${summary.total}`);
  console.log(`  Passed: ${summary.passed} ✅`);
  console.log(`  Failed: ${summary.failed} ❌`);
  console.log(`  Success rate: ${((summary.passed / summary.total) * 100).toFixed(1)}%`);

  if (summary.failed > 0) {
    console.log('\n❌ Failed Tests:');
    summary.results
      .filter(r => !r.passed)
      .forEach(result => {
        console.log(`  - ${result.name}: ${result.error}`);
      });
  }

  console.log('\n📋 Detailed Results:');
  summary.results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`  ${status} ${result.name}`);
    if (result.details && typeof result.details === 'object') {
      Object.entries(result.details).forEach(([key, value]) => {
        console.log(`      ${key}: ${JSON.stringify(value)}`);
      });
    }
  });

  if (summary.failed === 0) {
    console.log('\n🎉 All tests passed! The system is ready for use.');
  } else {
    console.log('\n⚠️  Some tests failed. Please address the issues before using the system.');
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
