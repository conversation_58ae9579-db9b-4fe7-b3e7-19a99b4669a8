import { ExtractionConfig } from '../src/types/index.js';

/**
 * Example configuration for the obligation extraction system
 * Copy this file to config.ts and update with your settings
 */
export const exampleConfig: ExtractionConfig = {
  // Gemini API Configuration
  geminiApiKey: process.env.GEMINI_API_KEY || 'your-gemini-api-key-here',
  
  // Rate Limiting Settings
  requestsPerMinute: 60,           // Gemini free tier allows 60 requests per minute
  maxConcurrent: 5,                // Maximum concurrent requests
  minTimeBetweenRequests: 1000,    // Minimum 1 second between requests
  
  // Retry Configuration
  retryDelay: 2000,                // 2 second delay between retries
  maxRetryTime: 30000,             // Maximum 30 seconds total retry time
  
  // Keyword Search Configuration
  customKeywords: [
    'covenant',
    'undertake',
    'bind',
    'obligate'
  ],
  
  // Confidence Thresholds
  minConfidenceThreshold: 0.6,     // Minimum confidence to process clause
  
  // Processing Options
  enableParallelProcessing: true,   // Process sub-elements in parallel
  enableTextAnchorValidation: true, // Validate and improve text anchors
  enableReferenceResolution: true,  // Resolve clause references
  
  // Review Flagging
  enableAutoReviewFlagging: true,   // Automatically flag issues for review
  reviewPriorityThreshold: 7,       // Flag items with priority >= 7 as urgent
  
  // Output Options
  includeHighlightedHtml: true,     // Generate HTML with highlighted obligations
  includeStatistics: true,          // Include extraction statistics
  includeValidationResults: false,  // Include LLM validation (uses extra API calls)
};

/**
 * Development configuration with more verbose logging and lower rate limits
 */
export const developmentConfig: ExtractionConfig = {
  ...exampleConfig,
  requestsPerMinute: 30,           // Lower rate limit for development
  maxConcurrent: 2,                // Fewer concurrent requests
  minTimeBetweenRequests: 2000,    // Longer delays between requests
  enableValidationLogging: true,   // Enable detailed validation logging
};

/**
 * Production configuration optimized for performance
 */
export const productionConfig: ExtractionConfig = {
  ...exampleConfig,
  requestsPerMinute: 60,           // Full rate limit
  maxConcurrent: 10,               // More concurrent requests
  minTimeBetweenRequests: 1000,    // Minimum required delay
  enableParallelProcessing: true,   // Full parallel processing
  includeValidationResults: false, // Skip validation to save API calls
};

/**
 * Testing configuration for unit tests
 */
export const testConfig: ExtractionConfig = {
  ...exampleConfig,
  geminiApiKey: 'test-api-key',
  requestsPerMinute: 1000,         // No rate limiting in tests
  maxConcurrent: 1,                // Sequential processing for predictable tests
  minTimeBetweenRequests: 0,       // No delays in tests
  enableParallelProcessing: false, // Sequential for deterministic results
};

/**
 * Get configuration based on environment
 */
export function getConfig(): ExtractionConfig {
  const env = process.env.NODE_ENV || 'development';
  
  switch (env) {
    case 'production':
      return productionConfig;
    case 'test':
      return testConfig;
    case 'development':
    default:
      return developmentConfig;
  }
}

/**
 * Validate configuration settings
 */
export function validateConfig(config: ExtractionConfig): string[] {
  const errors: string[] = [];
  
  if (!config.geminiApiKey || config.geminiApiKey === 'your-gemini-api-key-here') {
    errors.push('GEMINI_API_KEY is required. Set it in environment variables or config.');
  }
  
  if (config.requestsPerMinute <= 0) {
    errors.push('requestsPerMinute must be greater than 0');
  }
  
  if (config.maxConcurrent <= 0) {
    errors.push('maxConcurrent must be greater than 0');
  }
  
  if (config.minTimeBetweenRequests < 0) {
    errors.push('minTimeBetweenRequests cannot be negative');
  }
  
  if (config.minConfidenceThreshold < 0 || config.minConfidenceThreshold > 1) {
    errors.push('minConfidenceThreshold must be between 0 and 1');
  }
  
  return errors;
}
