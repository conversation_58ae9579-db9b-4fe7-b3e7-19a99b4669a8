# Obligation Extraction System - Examples

This directory contains example scripts and configurations to help you get started with the Obligation Extraction System.

## Quick Start

1. **Set up your environment**:
   ```bash
   # Copy the example configuration
   cp examples/config.example.ts examples/config.ts
   
   # Set your Gemini API key
   export GEMINI_API_KEY="your-api-key-here"
   ```

2. **Test the system**:
   ```bash
   npm run build
   node examples/test-system.js
   ```

3. **Try the basic example**:
   ```bash
   node examples/basic-usage.js
   ```

## Examples Overview

### 1. `config.example.ts`
Configuration templates for different environments:
- **Development**: Lower rate limits, verbose logging
- **Production**: Optimized for performance
- **Testing**: Sequential processing for predictable results

Key configuration options:
- `geminiApiKey`: Your Google Gemini API key
- `requestsPerMinute`: Rate limiting (60 for free tier)
- `maxConcurrent`: Concurrent request limit
- `minConfidenceThreshold`: Skip clauses below this confidence
- `enableParallelProcessing`: Process sub-elements in parallel

### 2. `test-system.ts`
Comprehensive integration tests that verify:
- ✅ Configuration validation
- ✅ Service instantiation
- ✅ Prompt loading and templating
- ✅ Keyword search functionality
- ✅ Text anchoring accuracy
- ✅ Reference resolution
- ✅ Review flagging logic
- ✅ End-to-end integration

Run with: `node examples/test-system.js`

### 3. `basic-usage.ts`
Simple single-clause processing example showing:
- System initialization and configuration
- Processing a single contract clause
- Displaying extracted obligations
- Generating highlighted HTML output
- Review item analysis
- Statistics and performance metrics

Run with: `node examples/basic-usage.js`

### 4. `batch-processing.ts`
Advanced batch processing example demonstrating:
- Processing multiple clauses efficiently
- Rate limiting and queue management
- Comprehensive reporting and statistics
- Export to JSON and CSV formats
- Performance analysis
- Review prioritization

Run with: `node examples/batch-processing.js`

## Expected Output

### Basic Usage Example
```
🚀 Starting Obligation Extraction System Example

✅ Configuration validated
🔧 Initializing services...
🔌 Testing Gemini API connection...
✅ Gemini API connection successful

📄 Analyzing sample clause:
Clause 4.2: 4.2 Delivery and Acceptance. The Contractor shall deliver the completed software...

🔍 Extracting obligations...

📊 Extraction Results:
  Contains obligations: true
  Number of obligations: 3
  Confidence score: 0.92
  Keywords found: shall, must, provide

📋 Extracted Obligations:

  1. DELIVERY OBLIGATION
     Party: Contractor
     Action: deliver the completed software application to the Client within ninety (90) days after the execution of this Agreement
     Text: "The Contractor shall deliver the completed software application to the Client within ninety (90) days after the execution of this Agreement"
     Anchors: "The Contractor shall deliver" ... "execution of this Agreement"
     Temporal: 1

  2. TESTING OBLIGATION
     Party: Client
     Action: test and evaluate the software within thirty (30) days from delivery
     Text: "The Client shall have thirty (30) days from delivery to test and evaluate the software"
     Anchors: "The Client shall have" ... "test and evaluate the software"
     Temporal: 1

  3. SUPPORT OBLIGATION
     Party: Contractor
     Action: provide technical support for a period of twelve (12) months
     Text: "the Contractor shall provide technical support for a period of twelve (12) months as described in Section 5.3"
     Anchors: "the Contractor shall provide" ... "described in Section 5.3"
     References: 1
     Temporal: 1

🎨 Generating highlighted HTML...
  HTML generated with 3 highlights
  💾 Saved highlighted clause to clause-analysis-result.html

📈 Extraction Statistics:
  Total clauses processed: 1
  Clauses with obligations: 1
  Total obligations found: 3
  Average obligations per clause: 3.0
  Average confidence: 0.92
  Clauses needing review: 0

🎉 Example completed successfully!
```

### Test System Output
```
🚀 Starting System Integration Tests

🧪 Testing: Configuration Validation
  ✅ PASSED
🧪 Testing: Service Instantiation
  ✅ PASSED
🧪 Testing: Prompt Loading
  ✅ PASSED
🧪 Testing: Keyword Search
  ✅ PASSED
🧪 Testing: Text Anchoring
  ✅ PASSED
🧪 Testing: Reference Resolution
  ✅ PASSED
🧪 Testing: Review Flagging
  ✅ PASSED
🧪 Testing: End-to-End Integration (Mock)
  ✅ PASSED

📊 Test Results Summary:
  Total tests: 8
  Passed: 8 ✅
  Failed: 0 ❌
  Success rate: 100.0%

🎉 All tests passed! The system is ready for use.
```

## Generated Files

The examples create several output files:

### From Basic Usage
- `clause-analysis-result.html`: Interactive HTML with highlighted obligations

### From Batch Processing
- `batch-results.json`: Complete extraction results with metadata
- `batch-summary.csv`: Summary statistics in CSV format

## Troubleshooting

### Common Issues

1. **API Key Error**:
   ```
   ❌ Configuration errors:
     - GEMINI_API_KEY is required. Set it in environment variables or config.
   ```
   **Solution**: Set your Gemini API key in environment variables or config file.

2. **Rate Limiting**:
   ```
   ❌ Failed to connect to Gemini API. Check your API key.
   ```
   **Solution**: Check your API key and rate limits. Free tier allows 60 requests/minute.

3. **Module Import Errors**:
   ```
   Error: Cannot find module
   ```
   **Solution**: Run `npm run build` first to compile TypeScript.

### Performance Tips

1. **Optimize Rate Limits**: Adjust `requestsPerMinute` based on your API tier
2. **Batch Processing**: Use batch processing for multiple clauses
3. **Confidence Threshold**: Set `minConfidenceThreshold` to skip low-confidence clauses
4. **Parallel Processing**: Enable `enableParallelProcessing` for faster sub-element analysis

## Next Steps

1. **Customize Configuration**: Modify `config.example.ts` for your use case
2. **Add Custom Keywords**: Extend the keyword list for domain-specific terms
3. **Integrate with Your System**: Use the services in your own application
4. **Create Custom Prompts**: Modify prompts in the `prompts/` directory
5. **Build Reports**: Use the statistics and review data for custom reporting

## Support

For issues or questions:
1. Check the main README.md for detailed documentation
2. Review the implementation notes in `Planning/implementationNote_extractionV2Plan.md`
3. Run the test system to verify your setup
4. Check the generated output files for debugging information
