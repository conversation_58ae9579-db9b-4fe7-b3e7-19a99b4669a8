#!/usr/bin/env node

/**
 * Basic usage example for the Obligation Extraction System
 * 
 * This example demonstrates how to:
 * 1. Set up the extraction system
 * 2. Process a single clause
 * 3. Handle the results
 * 4. Generate reports
 */

import { ObligationExtractorService } from '../src/services/obligation-extractor.service.js';
import { KeywordSearchService } from '../src/services/keyword-search.service.js';
import { QueueManagerService } from '../src/services/queue-manager.service.js';
import { PromptManagerService } from '../src/services/prompt-manager.service.js';
import { ReferenceResolverService } from '../src/services/reference-resolver.service.js';
import { TextAnchorService } from '../src/services/text-anchor.service.js';
import { ReviewFlagService } from '../src/services/review-flag.service.js';
import { getConfig, validateConfig } from './config.example.js';

async function main() {
  console.log('🚀 Starting Obligation Extraction System Example\n');

  // 1. Load and validate configuration
  const config = getConfig();
  const configErrors = validateConfig(config);
  
  if (configErrors.length > 0) {
    console.error('❌ Configuration errors:');
    configErrors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }

  console.log('✅ Configuration validated');

  // 2. Initialize services
  console.log('🔧 Initializing services...');
  
  const keywordSearch = new KeywordSearchService();
  const queueManager = new QueueManagerService(config);
  const promptManager = new PromptManagerService();
  const referenceResolver = new ReferenceResolverService();
  const textAnchor = new TextAnchorService();
  const reviewFlag = new ReviewFlagService();

  // Test connection to Gemini
  console.log('🔌 Testing Gemini API connection...');
  const connectionTest = await queueManager.testConnection();
  if (!connectionTest) {
    console.error('❌ Failed to connect to Gemini API. Check your API key.');
    process.exit(1);
  }
  console.log('✅ Gemini API connection successful');

  // 3. Initialize main extractor
  const extractor = new ObligationExtractorService(
    keywordSearch,
    queueManager,
    promptManager,
    referenceResolver,
    textAnchor
  );

  // 4. Example clause to analyze
  const sampleClause = `
    4.2 Delivery and Acceptance. The Contractor shall deliver the completed software 
    application to the Client within ninety (90) days after the execution of this 
    Agreement. The Client shall have thirty (30) days from delivery to test and 
    evaluate the software. If the software does not conform to the specifications 
    set forth in Exhibit A, the Client may reject the delivery and the Contractor 
    must remedy any defects within fifteen (15) days of written notice. Upon 
    acceptance, the Contractor shall provide technical support for a period of 
    twelve (12) months as described in Section 5.3.
  `.trim();

  console.log('\n📄 Analyzing sample clause:');
  console.log('Clause 4.2:', sampleClause.substring(0, 100) + '...\n');

  try {
    // 5. Extract obligations
    console.log('🔍 Extracting obligations...');
    const result = await extractor.extractObligationsFromClause(sampleClause, '4.2');

    // 6. Display results
    console.log('\n📊 Extraction Results:');
    console.log(`  Contains obligations: ${result.containsObligations}`);
    console.log(`  Number of obligations: ${result.obligations.length}`);
    console.log(`  Confidence score: ${result.confidence?.toFixed(2) || 'N/A'}`);
    
    if (result.keywordsFound) {
      console.log(`  Keywords found: ${result.keywordsFound.join(', ')}`);
    }

    // 7. Display each obligation
    if (result.obligations.length > 0) {
      console.log('\n📋 Extracted Obligations:');
      result.obligations.forEach((obligation, index) => {
        console.log(`\n  ${index + 1}. ${obligation.type.toUpperCase()} OBLIGATION`);
        console.log(`     Party: ${obligation.party}`);
        console.log(`     Action: ${obligation.action}`);
        console.log(`     Text: "${obligation.text}"`);
        console.log(`     Anchors: "${obligation.startingQuote}" ... "${obligation.endingQuote}"`);
        
        if (obligation.conditions.length > 0) {
          console.log(`     Conditions: ${obligation.conditions.length}`);
        }
        
        if (obligation.temporalExpressions.length > 0) {
          console.log(`     Temporal: ${obligation.temporalExpressions.length}`);
        }
        
        if (obligation.references.length > 0) {
          console.log(`     References: ${obligation.references.length}`);
        }
        
        if (obligation.needsReview) {
          console.log(`     ⚠️  Needs Review`);
        }
      });
    }

    // 8. Check for review items
    console.log('\n🔍 Checking for review items...');
    const reviewItems = reviewFlag.analyzeAndFlag([result]);
    
    if (reviewItems.length > 0) {
      console.log(`  Found ${reviewItems.length} items needing review:`);
      reviewItems.forEach((item, index) => {
        console.log(`    ${index + 1}. ${item.reason} (Priority: ${item.priority})`);
        console.log(`       ${item.details}`);
      });
    } else {
      console.log('  ✅ No review items found');
    }

    // 9. Generate highlighted HTML (if enabled)
    if (config.includeHighlightedHtml && result.obligations.length > 0) {
      console.log('\n🎨 Generating highlighted HTML...');
      const highlighted = textAnchor.highlightObligationInClause(
        result.clauseText,
        result.obligations
      );
      
      console.log('  HTML generated with', highlighted.highlights.length, 'highlights');
      
      // Save to file for viewing
      const fs = await import('fs');
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Clause Analysis Results</title>
          <style>${textAnchor.generateHighlightCSS()}</style>
        </head>
        <body>
          <h1>Clause 4.2 Analysis</h1>
          <div style="font-family: monospace; line-height: 1.6; padding: 20px; border: 1px solid #ccc;">
            ${highlighted.highlightedHtml}
          </div>
        </body>
        </html>
      `;
      
      fs.writeFileSync('clause-analysis-result.html', htmlContent);
      console.log('  💾 Saved highlighted clause to clause-analysis-result.html');
    }

    // 10. Display statistics
    if (config.includeStatistics) {
      console.log('\n📈 Extraction Statistics:');
      const stats = extractor.getExtractionStats([result]);
      console.log(`  Total clauses processed: ${stats.totalClauses}`);
      console.log(`  Clauses with obligations: ${stats.clausesWithObligations}`);
      console.log(`  Total obligations found: ${stats.totalObligations}`);
      console.log(`  Average obligations per clause: ${stats.averageObligationsPerClause.toFixed(1)}`);
      console.log(`  Average confidence: ${stats.averageConfidence.toFixed(2)}`);
      console.log(`  Clauses needing review: ${stats.clausesNeedingReview}`);
    }

    // 11. Display queue statistics
    console.log('\n⚡ Queue Statistics:');
    const queueStats = queueManager.getQueueStats();
    console.log(`  Requests completed: ${queueStats.done}`);
    console.log(`  Requests running: ${queueStats.running}`);
    console.log(`  Requests queued: ${queueStats.queued}`);

  } catch (error) {
    console.error('\n❌ Error during extraction:', error);
    process.exit(1);
  } finally {
    // 12. Cleanup
    console.log('\n🧹 Cleaning up...');
    await queueManager.shutdown();
    console.log('✅ Cleanup complete');
  }

  console.log('\n🎉 Example completed successfully!');
  console.log('\nNext steps:');
  console.log('  - Review the extracted obligations above');
  console.log('  - Open clause-analysis-result.html in your browser to see highlighted text');
  console.log('  - Try running with your own contract clauses');
  console.log('  - Adjust configuration in config.example.ts as needed');
}

// Run the example
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
