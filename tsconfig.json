{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": true}}